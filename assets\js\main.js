// ===== MAIN APPLICATION CONTROLLER =====

class PortfolioApp {
    constructor() {
        this.currentTheme = 'default';
        this.isLoading = true;
        this.activeFilter = 'all';
        this.activeCategory = 'all';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadContent();
        this.initializeComponents();
        this.handleLoading();
    }
    
    // Event Listeners
    setupEventListeners() {
        // Navigation
        document.addEventListener('DOMContentLoaded', () => {
            this.setupNavigation();
            this.setupScrollProgress();
            this.setupThemeToggle();
            this.setupResumeModal();
            this.setupContactForm();
            this.setupMobileMenu();
            this.setupBackToTop();
            this.setupBreadcrumb();
            this.setupKeyboardNavigation();
            this.setupGlobalSearch();
            this.setupFullscreen();
        });

        // Window events
        window.addEventListener('scroll', () => this.handleScroll());
        window.addEventListener('resize', () => this.handleResize());

        // Keyboard events
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }
    
    // Navigation Setup
    setupNavigation() {
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                this.scrollToSection(targetId);
                this.setActiveNavLink(link);
            });
        });
    }
    
    setActiveNavLink(activeLink) {
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }
    
    scrollToSection(sectionId) {
        animationController.scrollToElement(sectionId);
    }
    
    // Scroll Progress
    setupScrollProgress() {
        const progressBar = document.getElementById('progress-bar');
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset;
            const docHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            
            if (progressBar) {
                progressBar.style.width = scrollPercent + '%';
            }
            
            // Update active navigation based on scroll position
            this.updateActiveNavigation();
        });
    }
    
    updateActiveNavigation() {
        const sections = document.querySelectorAll('section[id]');
        const scrollPos = window.pageYOffset + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
                if (activeLink) {
                    this.setActiveNavLink(activeLink);
                }
            }
        });
    }
    
    // Theme Toggle
    setupThemeToggle() {
        const themeBtn = document.getElementById('theme-toggle');
        const themeKeys = Object.keys(themes);
        let currentThemeIndex = 0;
        
        if (themeBtn) {
            themeBtn.addEventListener('click', () => {
                currentThemeIndex = (currentThemeIndex + 1) % themeKeys.length;
                const newTheme = themeKeys[currentThemeIndex];
                this.changeTheme(newTheme);
            });
        }
    }
    
    changeTheme(themeName) {
        const theme = themes[themeName];
        if (!theme) return;
        
        const root = document.documentElement;
        root.style.setProperty('--primary-color', theme.primary);
        root.style.setProperty('--secondary-color', theme.secondary);
        root.style.setProperty('--accent-color', theme.accent);
        
        this.currentTheme = themeName;
        
        // Add theme change animation
        document.body.style.transition = 'all 0.5s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 500);
    }
    
    // Resume Modal
    setupResumeModal() {
        const resumeBtn = document.getElementById('resume-btn');
        const modal = document.getElementById('resume-modal');
        
        if (resumeBtn) {
            resumeBtn.addEventListener('click', () => this.openResumeModal());
        }
        
        // Close modal on outside click
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeResumeModal();
                }
            });
        }
    }
    
    openResumeModal() {
        const modal = document.getElementById('resume-modal');
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }
    
    closeResumeModal() {
        const modal = document.getElementById('resume-modal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
        }
    }
    
    downloadResume() {
        // Check if resume file exists
        const link = document.createElement('a');
        link.href = resumeConfig.pdfUrl;
        link.download = resumeConfig.downloadFilename;

        // For demo purposes, show a message if file doesn't exist
        link.onerror = () => {
            this.showNotification('Resume PDF not found. Please add your resume to assets/resume/Sanjai_S_Resume.pdf', 'error');
        };

        link.click();
    }

    viewResume() {
        const viewer = document.getElementById('resume-viewer');
        if (viewer) {
            // Check if we're running locally (file://) or on a server
            const isLocal = window.location.protocol === 'file:';

            if (isLocal) {
                viewer.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center; color: #fff;">
                        <i class="fas fa-file-pdf" style="font-size: 4rem; color: #00ffff; margin-bottom: 1rem;"></i>
                        <h3 style="margin-bottom: 1rem;">Resume Preview</h3>
                        <p style="margin-bottom: 2rem; color: #b8b8b8;">
                            Online preview requires a web server.<br>
                            Please use the download button to view the resume.
                        </p>
                        <button onclick="portfolioApp.downloadResume()" class="btn btn-primary">
                            <i class="fas fa-download"></i>
                            <span>Download Resume</span>
                        </button>
                    </div>
                `;
            } else {
                viewer.innerHTML = `
                    <iframe
                        src="${resumeConfig.viewerUrl}${encodeURIComponent(window.location.origin + '/' + resumeConfig.pdfUrl)}&embedded=true"
                        width="100%"
                        height="100%"
                        style="border: none;">
                    </iframe>
                `;
            }
        }
    }
    
    // Contact Form
    setupContactForm() {
        const form = document.getElementById('contact-form');
        
        if (form) {
            form.addEventListener('submit', (e) => this.handleContactSubmit(e));
        }
    }
    
    async handleContactSubmit(e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        // Show loading state
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Sending...</span>';
        submitBtn.disabled = true;

        try {
            // Demo mode for testing without server
            if (contactConfig.demoMode) {
                // Simulate network delay
                await new Promise(resolve => setTimeout(resolve, 1500));

                // Get form data for demo
                const name = formData.get('name');
                const email = formData.get('email');
                const subject = formData.get('subject');
                const message = formData.get('message');

                // Basic validation
                if (!name || !email || !subject || !message) {
                    throw new Error('Please fill in all fields');
                }

                // Show success message
                this.showNotification(`Demo Mode: Message from ${name} received! In production, this would be <NAME_EMAIL>`, 'success');
                form.reset();
                return;
            }

            // Production mode with server
            const response = await fetch(contactConfig.emailService, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                this.showNotification(contactConfig.successMessage, 'success');
                form.reset();
            } else {
                throw new Error('Network response was not ok');
            }
        } catch (error) {
            this.showNotification(error.message || contactConfig.errorMessage, 'error');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
    
    // Mobile Menu
    setupMobileMenu() {
        const hamburger = document.getElementById('hamburger');
        const navMenu = document.querySelector('.nav-menu');
        
        if (hamburger && navMenu) {
            hamburger.addEventListener('click', () => {
                hamburger.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
            
            // Close menu when clicking on a link
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    hamburger.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });
        }
    }

    // Back to Top Button
    setupBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');

        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', () => {
                animationController.scrollToElement('home');
            });

            // Show/hide based on scroll position
            window.addEventListener('scroll', () => {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('visible');
                } else {
                    backToTopBtn.classList.remove('visible');
                }
            });
        }
    }

    // Breadcrumb Navigation
    setupBreadcrumb() {
        const breadcrumb = document.getElementById('breadcrumb');
        const breadcrumbContainer = breadcrumb?.querySelector('.breadcrumb-container');

        if (!breadcrumbContainer) return;

        const sections = ['home', 'about', 'skills', 'projects', 'experience', 'contact'];
        const sectionIcons = {
            home: 'fas fa-home',
            about: 'fas fa-user',
            skills: 'fas fa-code',
            projects: 'fas fa-project-diagram',
            experience: 'fas fa-briefcase',
            contact: 'fas fa-envelope'
        };

        window.addEventListener('scroll', () => {
            const scrollPos = window.pageYOffset + 150;
            let currentSection = 'home';

            sections.forEach(section => {
                const element = document.getElementById(section);
                if (element && scrollPos >= element.offsetTop) {
                    currentSection = section;
                }
            });

            // Update breadcrumb
            breadcrumbContainer.innerHTML = `
                <span class="breadcrumb-item active" data-section="${currentSection}">
                    <i class="${sectionIcons[currentSection]}"></i>
                    <span>${currentSection.charAt(0).toUpperCase() + currentSection.slice(1)}</span>
                </span>
            `;

            // Show/hide breadcrumb
            if (currentSection !== 'home') {
                breadcrumb.classList.add('visible');
            } else {
                breadcrumb.classList.remove('visible');
            }
        });
    }

    // Keyboard Navigation
    setupKeyboardNavigation() {
        const helpToggle = document.getElementById('keyboard-help-toggle');
        const keyboardHelper = document.getElementById('keyboard-helper');

        if (helpToggle && keyboardHelper) {
            helpToggle.addEventListener('click', () => {
                keyboardHelper.classList.toggle('visible');
            });

            // Close on outside click
            keyboardHelper.addEventListener('click', (e) => {
                if (e.target === keyboardHelper) {
                    keyboardHelper.classList.remove('visible');
                }
            });
        }
    }

    handleKeyboard(e) {
        // Don't trigger shortcuts when typing in inputs
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') return;

        const key = e.key.toLowerCase();

        switch (key) {
            case 'h':
                this.scrollToSection('home');
                break;
            case 'a':
                this.scrollToSection('about');
                break;
            case 's':
                this.scrollToSection('skills');
                break;
            case 'p':
                this.scrollToSection('projects');
                break;
            case 'e':
                this.scrollToSection('experience');
                break;
            case 'c':
                this.scrollToSection('contact');
                break;
            case 't':
                document.getElementById('theme-toggle')?.click();
                break;
            case '?':
                document.getElementById('keyboard-help-toggle')?.click();
                break;
            case 'escape':
                // Close any open modals or overlays
                this.closeAllOverlays();
                break;
        }
    }

    closeAllOverlays() {
        document.getElementById('keyboard-helper')?.classList.remove('visible');
        document.getElementById('search-overlay')?.classList.remove('visible');
        this.closeResumeModal();
    }

    // Global Search
    setupGlobalSearch() {
        const searchToggle = document.getElementById('search-toggle');
        const searchOverlay = document.getElementById('search-overlay');
        const searchClose = document.getElementById('search-close');
        const searchInput = document.getElementById('global-search');
        const searchResults = document.getElementById('search-results');

        if (searchToggle && searchOverlay) {
            searchToggle.addEventListener('click', () => {
                searchOverlay.classList.add('visible');
                searchInput?.focus();
            });

            searchClose?.addEventListener('click', () => {
                searchOverlay.classList.remove('visible');
            });

            searchOverlay.addEventListener('click', (e) => {
                if (e.target === searchOverlay) {
                    searchOverlay.classList.remove('visible');
                }
            });

            // Search functionality
            searchInput?.addEventListener('input', (e) => {
                this.performGlobalSearch(e.target.value);
            });

            // Quick access suggestions
            document.querySelectorAll('.suggestion-item').forEach(item => {
                item.addEventListener('click', () => {
                    const target = item.dataset.target;
                    this.scrollToSection(target);
                    searchOverlay.classList.remove('visible');
                });
            });
        }
    }

    performGlobalSearch(query) {
        const searchResults = document.getElementById('search-results');
        if (!searchResults || !query.trim()) {
            searchResults.innerHTML = `
                <div class="search-suggestions">
                    <h4>Quick Access</h4>
                    <div class="suggestion-item" data-target="skills">
                        <i class="fas fa-code"></i>
                        <span>Technical Skills</span>
                    </div>
                    <div class="suggestion-item" data-target="projects">
                        <i class="fas fa-project-diagram"></i>
                        <span>Featured Projects</span>
                    </div>
                    <div class="suggestion-item" data-target="experience">
                        <i class="fas fa-briefcase"></i>
                        <span>Work Experience</span>
                    </div>
                    <div class="suggestion-item" data-target="contact">
                        <i class="fas fa-envelope"></i>
                        <span>Contact Information</span>
                    </div>
                </div>
            `;
            return;
        }

        const results = [];
        const searchTerm = query.toLowerCase();

        // Search skills
        skillsData.forEach(skill => {
            if (skill.name.toLowerCase().includes(searchTerm) ||
                skill.description.toLowerCase().includes(searchTerm)) {
                results.push({
                    type: 'skill',
                    title: skill.name,
                    description: skill.description,
                    section: 'skills'
                });
            }
        });

        // Search projects
        projectsData.forEach(project => {
            if (project.title.toLowerCase().includes(searchTerm) ||
                project.description.toLowerCase().includes(searchTerm) ||
                project.tags.some(tag => tag.toLowerCase().includes(searchTerm))) {
                results.push({
                    type: 'project',
                    title: project.title,
                    description: project.description,
                    section: 'projects'
                });
            }
        });

        // Search experience
        experienceData.forEach(exp => {
            if (exp.title.toLowerCase().includes(searchTerm) ||
                exp.company.toLowerCase().includes(searchTerm) ||
                exp.description.toLowerCase().includes(searchTerm)) {
                results.push({
                    type: 'experience',
                    title: exp.title,
                    description: exp.company,
                    section: 'experience'
                });
            }
        });

        this.displaySearchResults(results);
    }

    displaySearchResults(results) {
        const searchResults = document.getElementById('search-results');
        if (!searchResults) return;

        if (results.length === 0) {
            searchResults.innerHTML = `
                <div class="no-results">
                    <i class="fas fa-search"></i>
                    <p>No results found</p>
                </div>
            `;
            return;
        }

        const resultsHTML = results.map(result => `
            <div class="search-result-item" data-section="${result.section}">
                <div class="result-type">${result.type}</div>
                <h4>${result.title}</h4>
                <p>${result.description}</p>
            </div>
        `).join('');

        searchResults.innerHTML = `
            <div class="search-results-list">
                <h4>Search Results (${results.length})</h4>
                ${resultsHTML}
            </div>
        `;

        // Add click handlers to results
        document.querySelectorAll('.search-result-item').forEach(item => {
            item.addEventListener('click', () => {
                const section = item.dataset.section;
                this.scrollToSection(section);
                document.getElementById('search-overlay').classList.remove('visible');
            });
        });
    }

    // Fullscreen Toggle
    setupFullscreen() {
        const fullscreenBtn = document.getElementById('fullscreen-toggle');

        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', () => {
                this.toggleFullscreen();
            });

            // Update icon based on fullscreen state
            document.addEventListener('fullscreenchange', () => {
                const icon = fullscreenBtn.querySelector('i');
                if (document.fullscreenElement) {
                    icon.className = 'fas fa-compress';
                    fullscreenBtn.title = 'Exit Fullscreen';
                } else {
                    icon.className = 'fas fa-expand';
                    fullscreenBtn.title = 'Enter Fullscreen';
                }
            });
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable fullscreen:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Load Dynamic Content
    loadContent() {
        this.loadSkills();
        this.loadProjects();
        this.loadExperience();
        this.startTypewriter();
    }
    
    // Load Skills
    loadSkills() {
        const skillsGrid = document.getElementById('skills-grid');
        const categoryBtns = document.querySelectorAll('.category-btn');
        const viewBtns = document.querySelectorAll('.view-btn');

        if (!skillsGrid) return;

        // Setup category filters
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.activeCategory = btn.dataset.category;
                this.filterSkills();
                this.updateSkillsChart();
                this.updateSkillsStats();
            });
        });

        // Setup view toggle
        viewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.switchSkillsView(btn.dataset.view);
            });
        });

        this.setupSkillsChart();
        this.setupSkillsComparison();
        this.renderSkills();
        this.updateSkillsStats();
    }
    
    renderSkills() {
        const skillsGrid = document.getElementById('skills-grid');
        if (!skillsGrid) return;
        
        const filteredSkills = this.activeCategory === 'all' 
            ? skillsData 
            : skillsData.filter(skill => skill.category === this.activeCategory);
        
        skillsGrid.innerHTML = filteredSkills.map(skill => `
            <div class="skill-card" data-category="${skill.category}">
                <div class="skill-header">
                    <div class="skill-icon">${skill.icon}</div>
                    <div class="skill-info">
                        <h3>${skill.name}</h3>
                        <span class="skill-category">${skill.category.toUpperCase()}</span>
                    </div>
                </div>
                <div class="skill-progress">
                    <div class="progress-bar-container">
                        <div class="progress-bar-fill" data-level="${skill.level}"></div>
                    </div>
                    <div class="progress-label">
                        <span>Proficiency</span>
                        <span>${skill.level}%</span>
                    </div>
                </div>
                <p class="skill-description">${skill.description}</p>
            </div>
        `).join('');
        
        // Re-observe new elements
        setTimeout(() => {
            animationController.observeElements();
        }, 100);
    }
    
    filterSkills() {
        this.renderSkills();
    }

    // Switch between different skills views
    switchSkillsView(view) {
        const gridContainer = document.getElementById('skills-grid');
        const chartContainer = document.getElementById('skills-chart-container');
        const comparisonContainer = document.getElementById('skills-comparison-container');

        // Hide all containers
        gridContainer.style.display = 'none';
        chartContainer.style.display = 'none';
        comparisonContainer.style.display = 'none';

        // Show selected view
        switch (view) {
            case 'grid':
                gridContainer.style.display = 'grid';
                break;
            case 'chart':
                chartContainer.style.display = 'block';
                this.updateSkillsChart();
                break;
            case 'comparison':
                comparisonContainer.style.display = 'block';
                this.populateSkillSelectors();
                break;
        }
    }

    // Setup skills chart
    setupSkillsChart() {
        const canvas = document.getElementById('skills-chart');
        if (!canvas) return;

        this.chartCtx = canvas.getContext('2d');
        this.currentChartType = 'radar';

        // Setup chart type buttons
        document.querySelectorAll('.chart-type-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentChartType = btn.dataset.type;
                this.updateSkillsChart();
            });
        });
    }

    // Update skills chart
    updateSkillsChart() {
        if (!this.chartCtx) return;

        const filteredSkills = this.getFilteredSkills();
        const canvas = document.getElementById('skills-chart');

        // Clear canvas
        this.chartCtx.clearRect(0, 0, canvas.width, canvas.height);

        switch (this.currentChartType) {
            case 'radar':
                this.drawRadarChart(filteredSkills);
                break;
            case 'bar':
                this.drawBarChart(filteredSkills);
                break;
            case 'doughnut':
                this.drawDoughnutChart(filteredSkills);
                break;
        }

        this.updateChartLegend(filteredSkills);
    }

    getFilteredSkills() {
        return this.activeCategory === 'all'
            ? skillsData
            : skillsData.filter(skill => skill.category === this.activeCategory);
    }

    drawRadarChart(skills) {
        const canvas = document.getElementById('skills-chart');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 50;
        const angleStep = (Math.PI * 2) / skills.length;

        // Draw background grid
        this.chartCtx.strokeStyle = 'rgba(0, 255, 255, 0.2)';
        this.chartCtx.lineWidth = 1;

        for (let i = 1; i <= 5; i++) {
            this.chartCtx.beginPath();
            this.chartCtx.arc(centerX, centerY, (radius / 5) * i, 0, Math.PI * 2);
            this.chartCtx.stroke();
        }

        // Draw axes
        skills.forEach((skill, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;

            this.chartCtx.beginPath();
            this.chartCtx.moveTo(centerX, centerY);
            this.chartCtx.lineTo(x, y);
            this.chartCtx.stroke();

            // Draw labels
            this.chartCtx.fillStyle = '#ffffff';
            this.chartCtx.font = '12px Arial';
            this.chartCtx.textAlign = 'center';
            const labelX = centerX + Math.cos(angle) * (radius + 20);
            const labelY = centerY + Math.sin(angle) * (radius + 20);
            this.chartCtx.fillText(skill.name, labelX, labelY);
        });

        // Draw data
        this.chartCtx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
        this.chartCtx.fillStyle = 'rgba(0, 255, 255, 0.2)';
        this.chartCtx.lineWidth = 2;

        this.chartCtx.beginPath();
        skills.forEach((skill, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const distance = (skill.level / 100) * radius;
            const x = centerX + Math.cos(angle) * distance;
            const y = centerY + Math.sin(angle) * distance;

            if (index === 0) {
                this.chartCtx.moveTo(x, y);
            } else {
                this.chartCtx.lineTo(x, y);
            }
        });
        this.chartCtx.closePath();
        this.chartCtx.fill();
        this.chartCtx.stroke();

        // Draw data points
        skills.forEach((skill, index) => {
            const angle = index * angleStep - Math.PI / 2;
            const distance = (skill.level / 100) * radius;
            const x = centerX + Math.cos(angle) * distance;
            const y = centerY + Math.sin(angle) * distance;

            this.chartCtx.fillStyle = '#00ffff';
            this.chartCtx.beginPath();
            this.chartCtx.arc(x, y, 4, 0, Math.PI * 2);
            this.chartCtx.fill();
        });
    }

    drawBarChart(skills) {
        const canvas = document.getElementById('skills-chart');
        const padding = 50;
        const chartWidth = canvas.width - padding * 2;
        const chartHeight = canvas.height - padding * 2;
        const barWidth = chartWidth / skills.length - 10;
        const maxLevel = Math.max(...skills.map(s => s.level));

        // Draw axes
        this.chartCtx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        this.chartCtx.lineWidth = 1;

        // Y-axis
        this.chartCtx.beginPath();
        this.chartCtx.moveTo(padding, padding);
        this.chartCtx.lineTo(padding, canvas.height - padding);
        this.chartCtx.stroke();

        // X-axis
        this.chartCtx.beginPath();
        this.chartCtx.moveTo(padding, canvas.height - padding);
        this.chartCtx.lineTo(canvas.width - padding, canvas.height - padding);
        this.chartCtx.stroke();

        // Draw bars
        skills.forEach((skill, index) => {
            const barHeight = (skill.level / 100) * chartHeight;
            const x = padding + index * (barWidth + 10) + 5;
            const y = canvas.height - padding - barHeight;

            // Create gradient
            const gradient = this.chartCtx.createLinearGradient(0, y, 0, y + barHeight);
            gradient.addColorStop(0, '#00ffff');
            gradient.addColorStop(1, '#ff00ff');

            this.chartCtx.fillStyle = gradient;
            this.chartCtx.fillRect(x, y, barWidth, barHeight);

            // Draw skill name
            this.chartCtx.fillStyle = '#ffffff';
            this.chartCtx.font = '10px Arial';
            this.chartCtx.textAlign = 'center';
            this.chartCtx.save();
            this.chartCtx.translate(x + barWidth / 2, canvas.height - padding + 15);
            this.chartCtx.rotate(-Math.PI / 4);
            this.chartCtx.fillText(skill.name, 0, 0);
            this.chartCtx.restore();

            // Draw percentage
            this.chartCtx.fillStyle = '#ffffff';
            this.chartCtx.font = '12px Arial';
            this.chartCtx.textAlign = 'center';
            this.chartCtx.fillText(`${skill.level}%`, x + barWidth / 2, y - 5);
        });
    }
    
    drawDoughnutChart(skills) {
        const canvas = document.getElementById('skills-chart');
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(centerX, centerY) - 50;
        const innerRadius = radius * 0.6;

        let currentAngle = -Math.PI / 2;
        const total = skills.reduce((sum, skill) => sum + skill.level, 0);

        skills.forEach((skill, index) => {
            const sliceAngle = (skill.level / total) * Math.PI * 2;

            // Create gradient
            const gradient = this.chartCtx.createRadialGradient(centerX, centerY, innerRadius, centerX, centerY, radius);
            const hue = (index * 360) / skills.length;
            gradient.addColorStop(0, `hsl(${hue}, 100%, 70%)`);
            gradient.addColorStop(1, `hsl(${hue}, 100%, 50%)`);

            this.chartCtx.fillStyle = gradient;
            this.chartCtx.beginPath();
            this.chartCtx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            this.chartCtx.arc(centerX, centerY, innerRadius, currentAngle + sliceAngle, currentAngle, true);
            this.chartCtx.closePath();
            this.chartCtx.fill();

            // Draw percentage in the middle of slice
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelRadius = (radius + innerRadius) / 2;
            const labelX = centerX + Math.cos(labelAngle) * labelRadius;
            const labelY = centerY + Math.sin(labelAngle) * labelRadius;

            this.chartCtx.fillStyle = '#ffffff';
            this.chartCtx.font = 'bold 12px Arial';
            this.chartCtx.textAlign = 'center';
            this.chartCtx.fillText(`${skill.level}%`, labelX, labelY);

            currentAngle += sliceAngle;
        });
    }

    updateChartLegend(skills) {
        const legend = document.getElementById('chart-legend');
        if (!legend) return;

        legend.innerHTML = skills.map((skill, index) => {
            const hue = (index * 360) / skills.length;
            return `
                <div class="legend-item">
                    <div class="legend-color" style="background: hsl(${hue}, 100%, 60%);"></div>
                    <span class="legend-label">${skill.name}</span>
                    <span class="legend-value">${skill.level}%</span>
                </div>
            `;
        }).join('');
    }

    // Setup skills comparison
    setupSkillsComparison() {
        const selector1 = document.getElementById('skill-select-1');
        const selector2 = document.getElementById('skill-select-2');

        if (selector1 && selector2) {
            selector1.addEventListener('change', () => this.updateComparison());
            selector2.addEventListener('change', () => this.updateComparison());
        }
    }

    populateSkillSelectors() {
        const selector1 = document.getElementById('skill-select-1');
        const selector2 = document.getElementById('skill-select-2');

        if (!selector1 || !selector2) return;

        const filteredSkills = this.getFilteredSkills();
        const options = filteredSkills.map(skill =>
            `<option value="${skill.name}">${skill.name} (${skill.level}%)</option>`
        ).join('');

        selector1.innerHTML = '<option value="">Select first skill...</option>' + options;
        selector2.innerHTML = '<option value="">Select second skill...</option>' + options;
    }

    updateComparison() {
        const selector1 = document.getElementById('skill-select-1');
        const selector2 = document.getElementById('skill-select-2');
        const resultContainer = document.getElementById('comparison-result');

        if (!selector1 || !selector2 || !resultContainer) return;

        const skill1Name = selector1.value;
        const skill2Name = selector2.value;

        if (!skill1Name || !skill2Name) {
            resultContainer.innerHTML = `
                <div class="comparison-placeholder">
                    <i class="fas fa-balance-scale"></i>
                    <p>Select two skills to compare their proficiency levels</p>
                </div>
            `;
            return;
        }

        const skill1 = skillsData.find(s => s.name === skill1Name);
        const skill2 = skillsData.find(s => s.name === skill2Name);

        if (!skill1 || !skill2) return;

        const difference = Math.abs(skill1.level - skill2.level);
        const winner = skill1.level > skill2.level ? skill1 : skill2;
        const loser = skill1.level > skill2.level ? skill2 : skill1;

        resultContainer.innerHTML = `
            <div class="comparison-visual">
                <div class="skill-comparison-item ${skill1.level >= skill2.level ? 'winner' : ''}">
                    <div class="skill-icon">${skill1.icon}</div>
                    <h4>${skill1.name}</h4>
                    <div class="skill-level-bar">
                        <div class="skill-level-fill" style="width: ${skill1.level}%"></div>
                        <span class="skill-level-text">${skill1.level}%</span>
                    </div>
                    <p class="skill-category">${skill1.category.toUpperCase()}</p>
                </div>

                <div class="comparison-vs">
                    <div class="vs-circle">
                        <span>VS</span>
                    </div>
                    <div class="comparison-stats">
                        <div class="stat-item">
                            <span class="stat-label">Difference</span>
                            <span class="stat-value">${difference}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Winner</span>
                            <span class="stat-value">${winner.name}</span>
                        </div>
                    </div>
                </div>

                <div class="skill-comparison-item ${skill2.level >= skill1.level ? 'winner' : ''}">
                    <div class="skill-icon">${skill2.icon}</div>
                    <h4>${skill2.name}</h4>
                    <div class="skill-level-bar">
                        <div class="skill-level-fill" style="width: ${skill2.level}%"></div>
                        <span class="skill-level-text">${skill2.level}%</span>
                    </div>
                    <p class="skill-category">${skill2.category.toUpperCase()}</p>
                </div>
            </div>

            <div class="comparison-insights">
                <h4>Comparison Insights</h4>
                <div class="insights-grid">
                    <div class="insight-item">
                        <i class="fas fa-trophy"></i>
                        <span>${winner.name} leads by ${difference}%</span>
                    </div>
                    <div class="insight-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Both skills are ${skill1.category === skill2.category ? 'in the same category' : 'in different categories'}</span>
                    </div>
                    <div class="insight-item">
                        <i class="fas fa-star"></i>
                        <span>${skill1.level >= 90 || skill2.level >= 90 ? 'Expert level detected' : 'Room for improvement'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    // Update skills statistics
    updateSkillsStats() {
        const filteredSkills = this.getFilteredSkills();

        if (filteredSkills.length === 0) return;

        const levels = filteredSkills.map(s => s.level);
        const maxLevel = Math.max(...levels);
        const avgLevel = Math.round(levels.reduce((a, b) => a + b, 0) / levels.length);
        const expertSkills = filteredSkills.filter(s => s.level >= 90).length;
        const topSkill = filteredSkills.find(s => s.level === maxLevel);

        // Update DOM elements
        const topSkillLevel = document.getElementById('top-skill-level');
        const topSkillName = document.getElementById('top-skill-name');
        const avgSkillLevel = document.getElementById('avg-skill-level');
        const totalSkills = document.getElementById('total-skills');
        const expertSkillsCount = document.getElementById('expert-skills');

        if (topSkillLevel) topSkillLevel.textContent = `${maxLevel}%`;
        if (topSkillName) topSkillName.textContent = topSkill ? topSkill.name : '-';
        if (avgSkillLevel) avgSkillLevel.textContent = `${avgLevel}%`;
        if (totalSkills) totalSkills.textContent = `${filteredSkills.length} skills`;
        if (expertSkillsCount) expertSkillsCount.textContent = expertSkills;

        // Animate the stat values
        this.animateStatValues();
    }

    animateStatValues() {
        document.querySelectorAll('.stat-value').forEach(element => {
            element.style.transform = 'scale(1.1)';
            element.style.color = 'var(--primary-color)';

            setTimeout(() => {
                element.style.transform = 'scale(1)';
                element.style.color = '';
            }, 300);
        });
    }

    // Load Projects
    loadProjects() {
        const projectsGrid = document.getElementById('projects-grid');
        const filterBtns = document.querySelectorAll('.filter-btn');
        const searchInput = document.getElementById('project-search');
        const sortSelect = document.getElementById('project-sort');
        const viewBtns = document.querySelectorAll('.view-control-btn');

        if (!projectsGrid) return;

        this.currentProjectView = 'grid';
        this.currentProjectSort = 'default';

        // Setup filters
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.activeFilter = btn.dataset.filter;
                this.filterProjects();
                this.updateProjectStats();
            });
        });

        // Setup search
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.filterProjects();
                this.updateProjectStats();
            });
        }

        // Setup sorting
        if (sortSelect) {
            sortSelect.addEventListener('change', () => {
                this.currentProjectSort = sortSelect.value;
                this.filterProjects();
            });
        }

        // Setup view toggle
        viewBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                viewBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentProjectView = btn.dataset.view;
                this.switchProjectView();
            });
        });

        this.renderProjects();
        this.updateProjectStats();
    }
    
    renderProjects() {
        const projectsGrid = document.getElementById('projects-grid');
        if (!projectsGrid) return;

        const filteredProjects = this.getSortedProjects();

        // Update grid class based on view
        projectsGrid.className = `projects-${this.currentProjectView}`;

        if (this.currentProjectView === 'grid') {
            projectsGrid.innerHTML = filteredProjects.map(project => `
                <div class="project-card" data-category="${project.category}" data-project-id="${project.id}">
                    ${project.featured ? '<div class="featured-badge"><i class="fas fa-star"></i> Featured</div>' : ''}
                    <div class="project-image-container">
                        <img src="${project.image}" alt="${project.title}" class="project-image"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="image-placeholder" style="display: none;">
                            <i class="fas fa-image"></i>
                            <span>No Image</span>
                        </div>
                        <div class="project-overlay">
                            <button class="overlay-btn" onclick="portfolioApp.openProjectModal(${project.id})">
                                <i class="fas fa-eye"></i>
                                <span>View Details</span>
                            </button>
                        </div>
                    </div>
                    <div class="project-content">
                        <div class="project-header">
                            <h3 class="project-title">${project.title}</h3>
                            <span class="project-category">${project.category.toUpperCase()}</span>
                        </div>
                        <p class="project-description">${this.truncateText(project.description, 100)}</p>
                        <div class="project-tags">
                            ${project.tags.slice(0, 3).map(tag => `<span class="project-tag">${tag}</span>`).join('')}
                            ${project.tags.length > 3 ? `<span class="project-tag more">+${project.tags.length - 3}</span>` : ''}
                        </div>
                        <div class="project-links">
                            <a href="${project.github}" target="_blank" class="project-link" title="View Code">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="${project.demo}" target="_blank" class="project-link" title="Live Demo">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                            <button class="project-link" onclick="portfolioApp.openProjectModal(${project.id})" title="View Details">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        } else {
            // List view
            projectsGrid.innerHTML = filteredProjects.map(project => `
                <div class="project-list-item" data-category="${project.category}" data-project-id="${project.id}">
                    <div class="project-list-image">
                        <img src="${project.image}" alt="${project.title}" class="project-image"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="image-placeholder" style="display: none;">
                            <i class="fas fa-image"></i>
                        </div>
                        ${project.featured ? '<div class="featured-indicator"><i class="fas fa-star"></i></div>' : ''}
                    </div>
                    <div class="project-list-content">
                        <div class="project-list-header">
                            <h3 class="project-title">${project.title}</h3>
                            <span class="project-category">${project.category.toUpperCase()}</span>
                        </div>
                        <p class="project-description">${project.description}</p>
                        <div class="project-tags">
                            ${project.tags.map(tag => `<span class="project-tag">${tag}</span>`).join('')}
                        </div>
                    </div>
                    <div class="project-list-actions">
                        <a href="${project.github}" target="_blank" class="project-link" title="View Code">
                            <i class="fab fa-github"></i>
                            <span>Code</span>
                        </a>
                        <a href="${project.demo}" target="_blank" class="project-link" title="Live Demo">
                            <i class="fas fa-external-link-alt"></i>
                            <span>Demo</span>
                        </a>
                        <button class="project-link" onclick="portfolioApp.openProjectModal(${project.id})" title="View Details">
                            <i class="fas fa-info-circle"></i>
                            <span>Details</span>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Re-observe new elements
        setTimeout(() => {
            animationController.observeElements();
        }, 100);
    }

    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    switchProjectView() {
        this.renderProjects();
    }

    getSortedProjects() {
        const filteredProjects = this.getFilteredProjects();

        switch (this.currentProjectSort) {
            case 'name-asc':
                return filteredProjects.sort((a, b) => a.title.localeCompare(b.title));
            case 'name-desc':
                return filteredProjects.sort((a, b) => b.title.localeCompare(a.title));
            case 'featured':
                return filteredProjects.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0));
            case 'category':
                return filteredProjects.sort((a, b) => a.category.localeCompare(b.category));
            default:
                return filteredProjects;
        }
    }
    
    getFilteredProjects() {
        const searchTerm = document.getElementById('project-search')?.value.toLowerCase() || '';
        
        return projectsData.filter(project => {
            const matchesFilter = this.activeFilter === 'all' || project.category === this.activeFilter;
            const matchesSearch = project.title.toLowerCase().includes(searchTerm) ||
                                project.description.toLowerCase().includes(searchTerm) ||
                                project.tags.some(tag => tag.toLowerCase().includes(searchTerm));
            
            return matchesFilter && matchesSearch;
        });
    }
    
    filterProjects() {
        this.renderProjects();
    }

    // Project Modal Functions
    openProjectModal(projectId) {
        const project = projectsData.find(p => p.id === projectId);
        if (!project) return;

        const modal = document.getElementById('project-modal');
        const modalTitle = document.getElementById('project-modal-title');
        const modalBody = document.getElementById('project-modal-body');

        modalTitle.textContent = project.title;

        modalBody.innerHTML = `
            <div class="project-modal-content-wrapper">
                <div class="project-modal-image">
                    <img src="${project.image}" alt="${project.title}"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="image-placeholder" style="display: none;">
                        <i class="fas fa-image"></i>
                        <span>No Image Available</span>
                    </div>
                    ${project.featured ? '<div class="featured-badge-large"><i class="fas fa-star"></i> Featured Project</div>' : ''}
                </div>

                <div class="project-modal-details">
                    <div class="project-modal-header">
                        <div class="project-category-badge">${project.category.toUpperCase()}</div>
                        <div class="project-status">
                            ${project.featured ? '<span class="status-featured"><i class="fas fa-star"></i> Featured</span>' : ''}
                        </div>
                    </div>

                    <div class="project-description-full">
                        <h4>Project Overview</h4>
                        <p>${project.description}</p>
                    </div>

                    <div class="project-technologies">
                        <h4>Technologies Used</h4>
                        <div class="tech-tags">
                            ${project.tags.map(tag => `<span class="tech-tag">${tag}</span>`).join('')}
                        </div>
                    </div>

                    <div class="project-features">
                        <h4>Key Features</h4>
                        <ul class="features-list">
                            ${this.generateProjectFeatures(project).map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>

                    <div class="project-links-expanded">
                        <h4>Project Links</h4>
                        <div class="links-grid">
                            <a href="${project.github}" target="_blank" class="project-link-expanded">
                                <i class="fab fa-github"></i>
                                <div class="link-info">
                                    <span class="link-title">Source Code</span>
                                    <span class="link-desc">View on GitHub</span>
                                </div>
                            </a>
                            <a href="${project.demo}" target="_blank" class="project-link-expanded">
                                <i class="fas fa-external-link-alt"></i>
                                <div class="link-info">
                                    <span class="link-title">Live Demo</span>
                                    <span class="link-desc">Try it out</span>
                                </div>
                            </a>
                        </div>
                    </div>

                    <div class="project-stats">
                        <div class="stat-item">
                            <i class="fas fa-code"></i>
                            <span>Technologies: ${project.tags.length}</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-layer-group"></i>
                            <span>Category: ${project.category}</span>
                        </div>
                        <div class="stat-item">
                            <i class="fas fa-star"></i>
                            <span>Status: ${project.featured ? 'Featured' : 'Standard'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    closeProjectModal() {
        const modal = document.getElementById('project-modal');
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    generateProjectFeatures(project) {
        // Generate features based on project category and tags
        const features = [];

        if (project.category === 'ai') {
            features.push('Advanced machine learning algorithms');
            features.push('High accuracy prediction models');
            features.push('Real-time data processing');
        } else if (project.category === 'web') {
            features.push('Responsive design for all devices');
            features.push('Modern UI/UX interface');
            features.push('Cross-browser compatibility');
        } else if (project.category === 'automation') {
            features.push('Automated workflow processes');
            features.push('Real-time monitoring and alerts');
            features.push('Efficient data handling');
        }

        if (project.tags.includes('Python')) {
            features.push('Python-based implementation');
        }
        if (project.tags.includes('React')) {
            features.push('React component architecture');
        }
        if (project.tags.includes('TensorFlow')) {
            features.push('TensorFlow deep learning models');
        }

        return features.slice(0, 4); // Limit to 4 features
    }

    // Update project statistics
    updateProjectStats() {
        const filteredProjects = this.getFilteredProjects();
        const totalProjects = filteredProjects.length;
        const featuredProjects = filteredProjects.filter(p => p.featured).length;
        const categories = [...new Set(filteredProjects.map(p => p.category))].length;

        const totalElement = document.getElementById('total-projects');
        const featuredElement = document.getElementById('featured-projects');
        const categoriesElement = document.getElementById('project-categories');

        if (totalElement) totalElement.textContent = totalProjects;
        if (featuredElement) featuredElement.textContent = featuredProjects;
        if (categoriesElement) categoriesElement.textContent = categories;
    }
    
    // Load Experience
    loadExperience() {
        const timeline = document.getElementById('timeline');
        if (!timeline) return;
        
        timeline.innerHTML = experienceData.map((item, index) => `
            <div class="timeline-item">
                <div class="timeline-content">
                    <span class="timeline-date">${item.period}</span>
                    <h3 class="timeline-title">${item.title}</h3>
                    <div class="timeline-company">${item.company}</div>
                    <p class="timeline-description">${item.description}</p>
                    <div class="timeline-skills">
                        ${item.skills.map(skill => `<span class="timeline-skill">${skill}</span>`).join('')}
                    </div>
                </div>
            </div>
        `).join('');
        
        // Re-observe new elements
        setTimeout(() => {
            animationController.observeElements();
        }, 100);
    }
    
    // Typewriter Effect
    startTypewriter() {
        const typewriterElement = document.getElementById('typewriter');
        if (typewriterElement) {
            animationController.typewriter(typewriterElement, typewriterTexts, 100, 2000);
        }
    }
    
    // Loading Screen
    handleLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        
        // Simulate loading time
        setTimeout(() => {
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
            this.isLoading = false;
        }, 2000);
    }
    
    // Handle Scroll
    handleScroll() {
        const navbar = document.getElementById('navbar');
        
        if (window.scrollY > 50) {
            navbar?.classList.add('scrolled');
        } else {
            navbar?.classList.remove('scrolled');
        }
    }
    
    // Handle Resize
    handleResize() {
        // Responsive adjustments if needed
        if (window.innerWidth > 768) {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.getElementById('hamburger');
            
            navMenu?.classList.remove('active');
            hamburger?.classList.remove('active');
        }
    }
    
    // Initialize Components
    initializeComponents() {
        // Any additional component initialization
        this.setupAccessibility();
        this.startTypewriter();
        this.handleLoading();
    }

    // Start Typewriter Effect
    startTypewriter() {
        const typewriterElement = document.getElementById('typewriter');
        if (typewriterElement && typewriterTexts.length > 0) {
            animationController.typewriter(typewriterElement, typewriterTexts);
        }
    }

    setupAccessibility() {
        // Add ARIA labels and keyboard support
        document.querySelectorAll('button').forEach(button => {
            if (!button.getAttribute('aria-label') && button.title) {
                button.setAttribute('aria-label', button.title);
            }
        });

        // Add focus indicators
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });
    }

    // Handle Loading
    handleLoading() {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 500);
                }
                this.isLoading = false;
            }, 2000); // Show loading for 2 seconds minimum
        });
    }
}

// Global Functions
window.downloadResume = () => {
    const link = document.createElement('a');
    link.href = resumeConfig.pdfUrl;
    link.download = resumeConfig.downloadFilename;
    link.click();
};

window.viewResume = () => {
    const viewer = document.getElementById('resume-viewer');
    if (viewer) {
        viewer.innerHTML = `
            <iframe 
                src="${resumeConfig.viewerUrl}${encodeURIComponent(window.location.origin + '/' + resumeConfig.pdfUrl)}&embedded=true"
                width="100%" 
                height="100%" 
                style="border: none;">
            </iframe>
        `;
    }
};

window.closeResumeModal = () => {
    const modal = document.getElementById('resume-modal');
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }
};

window.closeProjectModal = () => {
    portfolioApp.closeProjectModal();
};

// Initialize Application
const portfolioApp = new PortfolioApp();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PortfolioApp;
}
