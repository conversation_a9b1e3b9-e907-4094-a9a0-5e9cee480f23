<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sanjai S - AI Developer & Machine Learning Engineer</title>
    <meta name="description" content="AI Developer and Machine Learning Engineer specializing in cutting-edge artificial intelligence solutions">
    <meta name="keywords" content="AI, Machine Learning, Deep Learning, Python, TensorFlow, Computer Vision">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen">
        <div class="loading-content">
            <div class="ai-logo">
                <div class="neural-network">
                    <div class="node"></div>
                    <div class="node"></div>
                    <div class="node"></div>
                    <div class="connection"></div>
                    <div class="connection"></div>
                </div>
            </div>
            <h2>Initializing AI Systems...</h2>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <nav id="navbar" class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-brain"></i>
                <span>SANJAI.AI</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#skills" class="nav-link">Skills</a></li>
                <li><a href="#projects" class="nav-link">Projects</a></li>
                <li><a href="#experience" class="nav-link">Experience</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="nav-controls">
                <button id="search-toggle" class="search-btn" title="Search">
                    <i class="fas fa-search"></i>
                </button>
                <button id="keyboard-help-toggle" class="help-btn" title="Keyboard Shortcuts (?)">
                    <i class="fas fa-keyboard"></i>
                </button>
                <button id="theme-toggle" class="theme-btn" title="Change Theme (T)">
                    <i class="fas fa-palette"></i>
                </button>
                <button id="fullscreen-toggle" class="fullscreen-btn" title="Toggle Fullscreen">
                    <i class="fas fa-expand"></i>
                </button>
                <button id="resume-btn" class="resume-btn" title="Download Resume">
                    <i class="fas fa-download"></i>
                    <span>Resume</span>
                </button>
                <div class="hamburger" id="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>

        <!-- Global Search Overlay -->
        <div id="search-overlay" class="search-overlay">
            <div class="search-container">
                <div class="search-header">
                    <h3>Search Portfolio</h3>
                    <button id="search-close" class="search-close">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="search-input-container">
                    <input type="text" id="global-search" placeholder="Search skills, projects, experience..." class="global-search-input">
                    <i class="fas fa-search search-input-icon"></i>
                </div>
                <div id="search-results" class="search-results">
                    <div class="search-suggestions">
                        <h4>Quick Access</h4>
                        <div class="suggestion-item" data-target="skills">
                            <i class="fas fa-code"></i>
                            <span>Technical Skills</span>
                        </div>
                        <div class="suggestion-item" data-target="projects">
                            <i class="fas fa-project-diagram"></i>
                            <span>Featured Projects</span>
                        </div>
                        <div class="suggestion-item" data-target="experience">
                            <i class="fas fa-briefcase"></i>
                            <span>Work Experience</span>
                        </div>
                        <div class="suggestion-item" data-target="contact">
                            <i class="fas fa-envelope"></i>
                            <span>Contact Information</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Progress Bar -->
    <div id="progress-bar" class="progress-bar"></div>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" title="Back to Top">
        <i class="fas fa-rocket"></i>
    </button>

    <!-- Breadcrumb Navigation -->
    <nav id="breadcrumb" class="breadcrumb">
        <div class="breadcrumb-container">
            <span class="breadcrumb-item active" data-section="home">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </span>
        </div>
    </nav>

    <!-- Keyboard Navigation Helper -->
    <div id="keyboard-helper" class="keyboard-helper">
        <div class="keyboard-helper-content">
            <h4>Keyboard Shortcuts</h4>
            <div class="shortcut-list">
                <div class="shortcut-item">
                    <kbd>H</kbd> <span>Home</span>
                </div>
                <div class="shortcut-item">
                    <kbd>A</kbd> <span>About</span>
                </div>
                <div class="shortcut-item">
                    <kbd>S</kbd> <span>Skills</span>
                </div>
                <div class="shortcut-item">
                    <kbd>P</kbd> <span>Projects</span>
                </div>
                <div class="shortcut-item">
                    <kbd>E</kbd> <span>Experience</span>
                </div>
                <div class="shortcut-item">
                    <kbd>C</kbd> <span>Contact</span>
                </div>
                <div class="shortcut-item">
                    <kbd>?</kbd> <span>Toggle Help</span>
                </div>
                <div class="shortcut-item">
                    <kbd>T</kbd> <span>Change Theme</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main>
        <!-- Hero Section -->
        <section id="home" class="hero">
            <div class="hero-background">
                <canvas id="neural-canvas"></canvas>
                <div class="floating-elements">
                    <div class="floating-icon" data-icon="🧠"></div>
                    <div class="floating-icon" data-icon="🤖"></div>
                    <div class="floating-icon" data-icon="⚡"></div>
                    <div class="floating-icon" data-icon="🔬"></div>
                    <div class="floating-icon" data-icon="💡"></div>
                </div>
            </div>
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        <span class="title-line">SANJAI S</span>
                        <span class="title-subtitle">AI DEVELOPER</span>
                    </h1>
                    <div class="typewriter-container">
                        <span id="typewriter" class="typewriter"></span>
                        <span class="cursor">|</span>
                    </div>
                    <p class="hero-description">
                        Crafting intelligent solutions through cutting-edge AI and machine learning technologies. 
                        Transforming complex data into actionable insights and building the future of artificial intelligence.
                    </p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary" onclick="scrollToSection('projects')">
                            <i class="fas fa-rocket"></i>
                            <span>View Projects</span>
                        </button>
                        <button class="btn btn-secondary" onclick="scrollToSection('contact')">
                            <i class="fas fa-envelope"></i>
                            <span>Get In Touch</span>
                        </button>
                    </div>
                </div>
                <div class="hero-avatar">
                    <div class="avatar-container">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=400" alt="Sanjai S" class="avatar-image">
                        <div class="avatar-glow"></div>
                        <div class="avatar-ring"></div>
                    </div>
                </div>
            </div>
            <div class="scroll-indicator">
                <div class="scroll-arrow">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">About Me</h2>
                    <div class="section-line"></div>
                    <p class="section-subtitle">Passionate AI Developer with a vision for the future</p>
                </div>
                <div class="about-content">
                    <div class="about-text">
                        <div class="about-card">
                            <h3>My Journey</h3>
                            <p>
                                B.Tech graduate in Artificial Intelligence & Data Science from Panimalar Engineering College. 
                                My passion lies in developing intelligent systems that solve real-world problems through 
                                innovative AI and machine learning approaches.
                            </p>
                            <p>
                                I specialize in computer vision, deep learning, and neural networks, with hands-on experience 
                                in building production-ready AI applications that make a meaningful impact.
                            </p>
                        </div>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number" data-target="15">0</div>
                                <div class="stat-label">Projects Completed</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-target="3">0</div>
                                <div class="stat-label">Years Experience</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-target="95">0</div>
                                <div class="stat-label">Success Rate %</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" data-target="24">0</div>
                                <div class="stat-label">Technologies</div>
                            </div>
                        </div>
                    </div>
                    <div class="about-visual">
                        <div class="ai-visualization">
                            <div class="brain-container">
                                <div class="brain-hemisphere left">
                                    <div class="neural-path"></div>
                                    <div class="neural-path"></div>
                                    <div class="neural-path"></div>
                                </div>
                                <div class="brain-hemisphere right">
                                    <div class="neural-path"></div>
                                    <div class="neural-path"></div>
                                    <div class="neural-path"></div>
                                </div>
                                <div class="brain-center">
                                    <i class="fas fa-microchip"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Skills Section -->
        <section id="skills" class="skills">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Technical Expertise</h2>
                    <div class="section-line"></div>
                    <p class="section-subtitle">Mastering the tools that power artificial intelligence</p>
                </div>
                <div class="skills-container">
                    <div class="skills-controls">
                        <div class="skills-categories">
                            <button class="category-btn active" data-category="all">All Skills</button>
                            <button class="category-btn" data-category="ai">AI & ML</button>
                            <button class="category-btn" data-category="programming">Programming</button>
                            <button class="category-btn" data-category="web">Web Dev</button>
                            <button class="category-btn" data-category="tools">Tools</button>
                        </div>
                        <div class="skills-view-toggle">
                            <button class="view-btn active" data-view="grid" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-btn" data-view="chart" title="Chart View">
                                <i class="fas fa-chart-bar"></i>
                            </button>
                            <button class="view-btn" data-view="comparison" title="Comparison View">
                                <i class="fas fa-balance-scale"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Skills Grid View -->
                    <div class="skills-grid" id="skills-grid">
                        <!-- Skills will be dynamically loaded -->
                    </div>

                    <!-- Skills Chart View -->
                    <div class="skills-chart-container" id="skills-chart-container" style="display: none;">
                        <div class="chart-header">
                            <h3>Skills Proficiency Chart</h3>
                            <div class="chart-controls">
                                <button class="chart-type-btn active" data-type="radar">Radar</button>
                                <button class="chart-type-btn" data-type="bar">Bar</button>
                                <button class="chart-type-btn" data-type="doughnut">Doughnut</button>
                            </div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="skills-chart" width="400" height="400"></canvas>
                        </div>
                        <div class="chart-legend" id="chart-legend"></div>
                    </div>

                    <!-- Skills Comparison View -->
                    <div class="skills-comparison-container" id="skills-comparison-container" style="display: none;">
                        <div class="comparison-header">
                            <h3>Skills Comparison Tool</h3>
                            <div class="comparison-selectors">
                                <select id="skill-select-1" class="skill-selector">
                                    <option value="">Select first skill...</option>
                                </select>
                                <span class="vs-text">VS</span>
                                <select id="skill-select-2" class="skill-selector">
                                    <option value="">Select second skill...</option>
                                </select>
                            </div>
                        </div>
                        <div class="comparison-result" id="comparison-result">
                            <div class="comparison-placeholder">
                                <i class="fas fa-balance-scale"></i>
                                <p>Select two skills to compare their proficiency levels</p>
                            </div>
                        </div>
                    </div>

                    <!-- Skills Statistics -->
                    <div class="skills-stats">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-trophy"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value" id="top-skill-level">0%</div>
                                <div class="stat-label">Highest Proficiency</div>
                                <div class="stat-detail" id="top-skill-name">-</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value" id="avg-skill-level">0%</div>
                                <div class="stat-label">Average Proficiency</div>
                                <div class="stat-detail" id="total-skills">0 skills</div>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-value" id="expert-skills">0</div>
                                <div class="stat-label">Expert Level</div>
                                <div class="stat-detail">90%+ proficiency</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Projects Section -->
        <section id="projects" class="projects">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Featured Projects</h2>
                    <div class="section-line"></div>
                    <p class="section-subtitle">Innovative AI solutions that make a difference</p>
                </div>
                <div class="projects-controls">
                    <div class="projects-search-container">
                        <input type="text" id="project-search" placeholder="Search projects..." class="search-input">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    <div class="projects-filters">
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all">All</button>
                            <button class="filter-btn" data-filter="ai">AI/ML</button>
                            <button class="filter-btn" data-filter="web">Web</button>
                            <button class="filter-btn" data-filter="automation">Automation</button>
                        </div>
                        <div class="sort-controls">
                            <select id="project-sort" class="sort-select">
                                <option value="default">Default Order</option>
                                <option value="name-asc">Name (A-Z)</option>
                                <option value="name-desc">Name (Z-A)</option>
                                <option value="featured">Featured First</option>
                                <option value="category">By Category</option>
                            </select>
                        </div>
                        <div class="view-controls">
                            <button class="view-control-btn active" data-view="grid" title="Grid View">
                                <i class="fas fa-th"></i>
                            </button>
                            <button class="view-control-btn" data-view="list" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Projects Grid/List View -->
                <div class="projects-container">
                    <div class="projects-grid" id="projects-grid">
                        <!-- Projects will be dynamically loaded -->
                    </div>

                    <!-- Projects Statistics -->
                    <div class="projects-stats">
                        <div class="projects-summary">
                            <div class="summary-item">
                                <span class="summary-label">Total Projects:</span>
                                <span class="summary-value" id="total-projects">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Featured:</span>
                                <span class="summary-value" id="featured-projects">0</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Categories:</span>
                                <span class="summary-value" id="project-categories">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Experience Section -->
        <section id="experience" class="experience">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Professional Journey</h2>
                    <div class="section-line"></div>
                    <p class="section-subtitle">Building expertise through hands-on experience</p>
                </div>
                <div class="timeline" id="timeline">
                    <!-- Timeline items will be dynamically loaded -->
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section id="contact" class="contact">
            <div class="container">
                <div class="section-header">
                    <h2 class="section-title">Let's Connect</h2>
                    <div class="section-line"></div>
                    <p class="section-subtitle">Ready to collaborate on the next big AI breakthrough?</p>
                </div>
                <div class="contact-content">
                    <div class="contact-info">
                        <div class="contact-card">
                            <h3>Get In Touch</h3>
                            <div class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <div>
                                    <span>Email</span>
                                    <a href="mailto:<EMAIL>"><EMAIL></a>
                                </div>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-phone"></i>
                                <div>
                                    <span>Phone</span>
                                    <a href="tel:+************">+91 8072686247</a>
                                </div>
                            </div>
                            <div class="contact-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div>
                                    <span>Location</span>
                                    <span>Tamil Nadu, India</span>
                                </div>
                            </div>
                        </div>
                        <div class="social-links">
                            <a href="https://github.com/sanjai827054" target="_blank" class="social-link">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="https://linkedin.com/in/sanjai-s" target="_blank" class="social-link">
                                <i class="fab fa-linkedin"></i>
                            </a>
                            <a href="https://twitter.com/sanjai_ai" target="_blank" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://instagram.com/sanjai.ai" target="_blank" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                    <div class="contact-form-container">
                        <form id="contact-form" class="contact-form">
                            <div class="form-group">
                                <input type="text" id="name" name="name" required>
                                <label for="name">Your Name</label>
                            </div>
                            <div class="form-group">
                                <input type="email" id="email" name="email" required>
                                <label for="email">Your Email</label>
                            </div>
                            <div class="form-group">
                                <input type="text" id="subject" name="subject" required>
                                <label for="subject">Subject</label>
                            </div>
                            <div class="form-group">
                                <textarea id="message" name="message" rows="5" required></textarea>
                                <label for="message">Your Message</label>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                <span>Send Message</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-text">
                    <p>&copy; 2024 Sanjai S. All rights reserved. Built with passion for AI innovation.</p>
                </div>
                <div class="footer-links">
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#terms">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Project Detail Modal -->
    <div id="project-modal" class="modal">
        <div class="modal-content project-modal-content">
            <div class="modal-header">
                <h3 id="project-modal-title">Project Details</h3>
                <button class="modal-close" onclick="closeProjectModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="project-modal-body" id="project-modal-body">
                    <!-- Project details will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Resume Modal -->
    <div id="resume-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Resume - Sanjai S</h3>
                <button class="modal-close" onclick="closeResumeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="resume-actions">
                    <button class="btn btn-primary" onclick="downloadResume()">
                        <i class="fas fa-download"></i>
                        Download PDF
                    </button>
                    <button class="btn btn-secondary" onclick="viewResume()">
                        <i class="fas fa-eye"></i>
                        View Online
                    </button>
                </div>
                <div id="resume-viewer" class="resume-viewer">
                    <!-- PDF viewer will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/data.js"></script>
    <script src="assets/js/animations.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
