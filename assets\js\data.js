// ===== DATA CONFIGURATION =====

// Skills Data
const skillsData = [
    // AI & Machine Learning
    {
        name: "Machine Learning",
        category: "ai",
        level: 90,
        icon: "🧠",
        description: "Deep learning, neural networks, computer vision, and predictive modeling"
    },
    {
        name: "TensorFlow",
        category: "ai",
        level: 85,
        icon: "🔥",
        description: "Building and deploying ML models with TensorFlow and Keras"
    },
    {
        name: "Computer Vision",
        category: "ai",
        level: 88,
        icon: "👁️",
        description: "Image processing, object detection, and CNN architectures"
    },
    {
        name: "Natural Language Processing",
        category: "ai",
        level: 75,
        icon: "💬",
        description: "Text analysis, sentiment analysis, and language models"
    },
    
    // Programming Languages
    {
        name: "Python",
        category: "programming",
        level: 95,
        icon: "🐍",
        description: "Data science, web development, automation, and AI applications"
    },
    {
        name: "JavaScript",
        category: "programming",
        level: 88,
        icon: "⚡",
        description: "Frontend and backend development with modern ES6+ features"
    },
    {
        name: "TypeScript",
        category: "programming",
        level: 82,
        icon: "📘",
        description: "Type-safe development for large-scale applications"
    },
    {
        name: "Java",
        category: "programming",
        level: 78,
        icon: "☕",
        description: "Object-oriented programming and enterprise applications"
    },
    
    // Web Development
    {
        name: "React",
        category: "web",
        level: 90,
        icon: "⚛️",
        description: "Modern UI development with hooks and state management"
    },
    {
        name: "Node.js",
        category: "web",
        level: 85,
        icon: "🟢",
        description: "Backend APIs, microservices, and real-time applications"
    },
    {
        name: "HTML5 & CSS3",
        category: "web",
        level: 92,
        icon: "🎨",
        description: "Responsive design, animations, and modern web standards"
    },
    {
        name: "PHP",
        category: "web",
        level: 80,
        icon: "🐘",
        description: "Server-side development and database integration"
    },
    
    // Tools & Technologies
    {
        name: "Git & GitHub",
        category: "tools",
        level: 88,
        icon: "🔧",
        description: "Version control, collaboration, and CI/CD workflows"
    },
    {
        name: "Docker",
        category: "tools",
        level: 75,
        icon: "🐳",
        description: "Containerization and deployment automation"
    },
    {
        name: "MySQL",
        category: "tools",
        level: 85,
        icon: "🗄️",
        description: "Database design, optimization, and complex queries"
    },
    {
        name: "MongoDB",
        category: "tools",
        level: 78,
        icon: "🍃",
        description: "NoSQL databases and document-based data modeling"
    }
];

// Projects Data
const projectsData = [
    {
        id: 1,
        title: "Skin Disease Prediction System",
        category: "ai",
        image: "assets/images/projects/skin-disease-prediction.jpg",
        description: "Advanced CNN-based system for early detection and classification of skin diseases with 85%+ accuracy using deep learning techniques.",
        tags: ["Python", "TensorFlow", "CNN", "Computer Vision", "Healthcare"],
        github: "https://github.com/sanjai827054/skin-disease-prediction",
        demo: "#",
        featured: true
    },
    {
        id: 2,
        title: "Crop Condition Detection",
        category: "ai",
        image: "assets/images/projects/crop-detection.jpg",
        description: "AI-powered agricultural solution using VGG-16 architecture to analyze crop health and predict optimal harvest times.",
        tags: ["Python", "VGG-16", "Agriculture", "Deep Learning", "OpenCV"],
        github: "https://github.com/sanjai827054/crop-detection",
        demo: "#",
        featured: true
    },
    {
        id: 3,
        title: "Gesture-Controlled Media Player",
        category: "automation",
        image: "assets/images/projects/gesture-media-player.jpg",
        description: "Innovative accessibility solution enabling hands-free media control through computer vision and gesture recognition.",
        tags: ["Python", "OpenCV", "MediaPipe", "Accessibility", "Real-time"],
        github: "https://github.com/sanjai827054/gesture-media-player",
        demo: "#",
        featured: false
    },
    {
        id: 4,
        title: "Smart Price Monitor",
        category: "automation",
        image: "assets/images/projects/price-monitor.jpg",
        description: "Automated price tracking system with email notifications for e-commerce platforms using web scraping techniques.",
        tags: ["Python", "BeautifulSoup", "Automation", "Web Scraping", "Email"],
        github: "https://github.com/sanjai827054/price-monitor",
        demo: "#",
        featured: false
    },
    {
        id: 5,
        title: "Dynamic Portfolio Website",
        category: "web",
        image: "assets/images/projects/portfolio-website.jpg",
        description: "Modern, responsive portfolio website with AI-themed design, interactive animations, and dynamic content management.",
        tags: ["HTML5", "CSS3", "JavaScript", "Responsive", "Animations"],
        github: "https://github.com/sanjai827054/portfolio",
        demo: "#",
        featured: true
    },
    {
        id: 6,
        title: "Real-time Chat Application",
        category: "web",
        image: "assets/images/projects/chat-application.jpg",
        description: "Full-stack chat application with real-time messaging, user authentication, and modern UI/UX design.",
        tags: ["React", "Node.js", "Socket.io", "MongoDB", "JWT"],
        github: "https://github.com/sanjai827054/chat-app",
        demo: "#",
        featured: false
    }
];

// Experience Data
const experienceData = [
    {
        id: 1,
        title: "Web Development Intern",
        company: "CAUSEVE TECHNOLOGIES LLP",
        period: "2024 - Present",
        type: "internship",
        description: "Developing dynamic web applications using modern technologies. Gained hands-on experience in full-stack development, database management, and client collaboration.",
        skills: ["HTML5", "CSS3", "JavaScript", "PHP", "MySQL", "Responsive Design"],
        achievements: [
            "Completed 5+ client projects with 100% satisfaction rate",
            "Implemented responsive designs for mobile-first approach",
            "Optimized database queries improving performance by 40%"
        ]
    },
    {
        id: 2,
        title: "Python Developer Intern",
        company: "CYBERNAUT EDUTECH PVT LTD",
        period: "2023 - 2024",
        type: "internship",
        description: "Specialized in automation and web scraping projects. Developed intelligent systems for data extraction and automated monitoring solutions.",
        skills: ["Python", "BeautifulSoup", "Selenium", "Automation", "Data Analysis"],
        achievements: [
            "Built automated price monitoring system",
            "Reduced manual data collection time by 80%",
            "Implemented email notification system for real-time alerts"
        ]
    },
    {
        id: 3,
        title: "B.Tech in AI & Data Science",
        company: "Panimalar Engineering College",
        period: "2021 - 2025",
        type: "education",
        description: "Comprehensive study of artificial intelligence, machine learning, and data science with focus on practical applications and research projects.",
        skills: ["Machine Learning", "Deep Learning", "Data Science", "Python", "Statistics"],
        achievements: [
            "Maintained excellent academic performance",
            "Completed 10+ AI/ML projects",
            "Participated in technical symposiums and hackathons"
        ]
    },
    {
        id: 4,
        title: "Higher Secondary Education",
        company: "Government Higher Secondary School",
        period: "2019 - 2021",
        type: "education",
        description: "Specialized in Mathematics, Physics, and Computer Science. Built strong foundation in analytical thinking and problem-solving.",
        skills: ["Mathematics", "Physics", "Computer Science", "C Programming"],
        achievements: [
            "Achieved excellent academic results",
            "Developed foundational programming skills",
            "Participated in science exhibitions"
        ]
    }
];

// Typewriter Text Array
const typewriterTexts = [
    "Building Intelligent Systems",
    "Machine Learning Engineer",
    "AI Solutions Developer",
    "Data Science Enthusiast",
    "Computer Vision Expert",
    "Full Stack Developer",
    "Innovation Through Code"
];

// Theme Configuration
const themes = {
    default: {
        name: "Cyber Blue",
        primary: "#00ffff",
        secondary: "#ff00ff",
        accent: "#00ff00"
    },
    neon: {
        name: "Neon Pink",
        primary: "#ff0080",
        secondary: "#00ff80",
        accent: "#8000ff"
    },
    matrix: {
        name: "Matrix Green",
        primary: "#00ff00",
        secondary: "#80ff00",
        accent: "#00ff80"
    },
    sunset: {
        name: "Sunset Orange",
        primary: "#ff8000",
        secondary: "#ff0040",
        accent: "#ffff00"
    }
};

// Contact Form Configuration
const contactConfig = {
    emailService: "contact.php", // PHP contact form handler
    successMessage: "Thank you for your message! I'll get back to you soon.",
    errorMessage: "Sorry, there was an error sending your message. Please try again.",
    // For testing without server, we'll use a demo mode
    demoMode: true, // Set to false when using with a web server
    // Alternative services you can use:
    // emailService: "https://formspree.io/f/your-form-id", // Formspree
    // emailService: "https://getform.io/f/your-form-id", // GetForm
    // emailService: "https://formsubmit.co/<EMAIL>", // FormSubmit
};

// Resume Configuration
const resumeConfig = {
    pdfUrl: "assets/resume/Sanjai_S_Resume.pdf", // Path to your resume PDF
    viewerUrl: "https://docs.google.com/viewer?url=", // Google Docs viewer for online viewing
    downloadFilename: "Sanjai_S_AI_Developer_Resume.pdf"
};

// Animation Configuration
const animationConfig = {
    observerOptions: {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px"
    },
    typewriterSpeed: 100,
    typewriterDelay: 2000,
    countUpDuration: 2000,
    particleCount: 50,
    particleSpeed: 0.5
};

// Export data for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        skillsData,
        projectsData,
        experienceData,
        typewriterTexts,
        themes,
        contactConfig,
        resumeConfig,
        animationConfig
    };
}
