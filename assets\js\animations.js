// ===== ANIMATION UTILITIES =====

class AnimationController {
    constructor() {
        this.observers = new Map();
        this.particles = [];
        this.canvas = null;
        this.ctx = null;
        this.animationId = null;
        
        this.init();
    }
    
    init() {
        this.setupIntersectionObserver();
        this.setupParticleSystem();
        this.setupScrollAnimations();
        this.addInteractiveEffects();
    }
    
    // Intersection Observer for scroll animations
    setupIntersectionObserver() {
        const options = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        this.observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    
                    // Trigger specific animations based on element type
                    if (entry.target.classList.contains('stat-number')) {
                        this.animateCounter(entry.target);
                    }
                    
                    if (entry.target.classList.contains('skill-card')) {
                        this.animateSkillBar(entry.target);
                    }
                    
                    if (entry.target.classList.contains('project-card')) {
                        this.animateProjectCard(entry.target);
                    }
                }
            });
        }, options);
        
        // Observe elements when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            this.observeElements();
        });
    }
    
    observeElements() {
        const elementsToObserve = [
            '.stat-number',
            '.skill-card',
            '.project-card',
            '.timeline-item',
            '.about-card',
            '.contact-card'
        ];
        
        elementsToObserve.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                this.observer.observe(el);
            });
        });
    }
    
    // Counter animation
    animateCounter(element) {
        const target = parseInt(element.dataset.target);
        const duration = 2000;
        const start = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(easeOutQuart * target);
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                element.textContent = target;
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    // Skill bar animation
    animateSkillBar(skillCard) {
        const progressBar = skillCard.querySelector('.progress-bar-fill');
        if (progressBar) {
            const level = progressBar.dataset.level || 0;
            setTimeout(() => {
                progressBar.style.width = level + '%';
            }, 200);
        }
    }
    
    // Project card animation
    animateProjectCard(card) {
        card.style.transform = 'translateY(50px)';
        card.style.opacity = '0';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.transform = 'translateY(0)';
            card.style.opacity = '1';
        }, 100);
    }
    
    // Particle system for neural network background
    setupParticleSystem() {
        this.canvas = document.getElementById('neural-canvas');
        if (!this.canvas) return;

        this.ctx = this.canvas.getContext('2d');
        this.resizeCanvas();

        window.addEventListener('resize', () => this.resizeCanvas());
        this.setupMouseTracking();

        this.createEnhancedParticles();
        this.animateEnhancedParticles();
    }

    setupMouseTracking() {
        this.mouseX = undefined;
        this.mouseY = undefined;

        document.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouseX = e.clientX - rect.left;
            this.mouseY = e.clientY - rect.top;
        });

        document.addEventListener('mouseleave', () => {
            this.mouseX = undefined;
            this.mouseY = undefined;
        });
    }
    
    resizeCanvas() {
        if (!this.canvas) return;
        
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
    }
    
    createParticles() {
        const particleCount = Math.min(50, Math.floor(window.innerWidth / 20));
        
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2,
                color: this.getRandomColor()
            });
        }
    }
    
    getRandomColor() {
        const colors = ['#00ffff', '#ff00ff', '#00ff00', '#ffff00', '#ff0080', '#8000ff'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // Enhanced particle creation with different types
    createEnhancedParticles() {
        const particleCount = Math.min(60, Math.floor(window.innerWidth / 15));
        this.particles = []; // Reset particles

        for (let i = 0; i < particleCount; i++) {
            const particleType = Math.random();
            let particle;

            if (particleType < 0.6) {
                // Standard particles
                particle = this.createStandardParticle();
            } else if (particleType < 0.8) {
                // Pulsing particles
                particle = this.createPulsingParticle();
            } else {
                // Morphing particles
                particle = this.createMorphingParticle();
            }

            this.particles.push(particle);
        }
    }

    createStandardParticle() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            vx: (Math.random() - 0.5) * 0.8,
            vy: (Math.random() - 0.5) * 0.8,
            size: Math.random() * 4 + 1,
            opacity: Math.random() * 0.6 + 0.2,
            color: this.getRandomColor(),
            type: 'standard',
            life: 1.0,
            maxLife: 1.0
        };
    }

    createPulsingParticle() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            size: Math.random() * 6 + 2,
            baseSize: Math.random() * 6 + 2,
            opacity: Math.random() * 0.8 + 0.3,
            color: this.getRandomColor(),
            type: 'pulsing',
            pulseSpeed: Math.random() * 0.05 + 0.02,
            pulsePhase: Math.random() * Math.PI * 2,
            life: 1.0,
            maxLife: 1.0
        };
    }

    createMorphingParticle() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            vx: (Math.random() - 0.5) * 0.3,
            vy: (Math.random() - 0.5) * 0.3,
            size: Math.random() * 8 + 3,
            opacity: Math.random() * 0.7 + 0.2,
            color: this.getRandomColor(),
            type: 'morphing',
            morphSpeed: Math.random() * 0.03 + 0.01,
            morphPhase: Math.random() * Math.PI * 2,
            sides: Math.floor(Math.random() * 6) + 3,
            life: 1.0,
            maxLife: 1.0
        };
    }
    
    animateEnhancedParticles() {
        if (!this.ctx || !this.canvas) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Update and draw particles
        this.particles.forEach((particle, index) => {
            this.updateParticle(particle);
            this.drawParticle(particle);
        });

        // Draw enhanced connections
        this.drawEnhancedConnections();

        // Add wave effects
        this.drawWaveEffects();

        this.animationId = requestAnimationFrame(() => this.animateEnhancedParticles());
    }

    updateParticle(particle) {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Wrap around edges with smooth transition
        if (particle.x < -particle.size) particle.x = this.canvas.width + particle.size;
        if (particle.x > this.canvas.width + particle.size) particle.x = -particle.size;
        if (particle.y < -particle.size) particle.y = this.canvas.height + particle.size;
        if (particle.y > this.canvas.height + particle.size) particle.y = -particle.size;

        // Update particle-specific properties
        if (particle.type === 'pulsing') {
            particle.pulsePhase += particle.pulseSpeed;
            particle.size = particle.baseSize + Math.sin(particle.pulsePhase) * particle.baseSize * 0.3;
        } else if (particle.type === 'morphing') {
            particle.morphPhase += particle.morphSpeed;
        }

        // Add subtle gravity effect
        particle.vy += 0.001;

        // Add mouse interaction
        this.addMouseInteraction(particle);
    }

    drawParticle(particle) {
        this.ctx.save();
        this.ctx.globalAlpha = particle.opacity;
        this.ctx.fillStyle = particle.color;
        this.ctx.shadowBlur = 15;
        this.ctx.shadowColor = particle.color;

        if (particle.type === 'morphing') {
            this.drawMorphingShape(particle);
        } else {
            // Standard and pulsing particles
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }

    drawMorphingShape(particle) {
        const sides = particle.sides;
        const radius = particle.size;
        const morphFactor = Math.sin(particle.morphPhase) * 0.3 + 1;

        this.ctx.beginPath();
        for (let i = 0; i < sides; i++) {
            const angle = (i / sides) * Math.PI * 2;
            const x = particle.x + Math.cos(angle) * radius * morphFactor;
            const y = particle.y + Math.sin(angle) * radius * morphFactor;

            if (i === 0) {
                this.ctx.moveTo(x, y);
            } else {
                this.ctx.lineTo(x, y);
            }
        }
        this.ctx.closePath();
        this.ctx.fill();
    }

    addMouseInteraction(particle) {
        if (this.mouseX !== undefined && this.mouseY !== undefined) {
            const dx = this.mouseX - particle.x;
            const dy = this.mouseY - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
                const force = (100 - distance) / 100;
                particle.vx += (dx / distance) * force * 0.01;
                particle.vy += (dy / distance) * force * 0.01;

                // Increase opacity near mouse
                particle.opacity = Math.min(1, particle.opacity + force * 0.3);
            }
        }
    }
    
    drawEnhancedConnections() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const dx = this.particles[i].x - this.particles[j].x;
                const dy = this.particles[i].y - this.particles[j].y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 120) {
                    this.ctx.save();

                    // Dynamic opacity based on distance and particle types
                    let opacity = (120 - distance) / 120 * 0.4;
                    if (this.particles[i].type === 'pulsing' || this.particles[j].type === 'pulsing') {
                        opacity *= 1.5;
                    }

                    this.ctx.globalAlpha = opacity;

                    // Create gradient line
                    const gradient = this.ctx.createLinearGradient(
                        this.particles[i].x, this.particles[i].y,
                        this.particles[j].x, this.particles[j].y
                    );
                    gradient.addColorStop(0, this.particles[i].color);
                    gradient.addColorStop(1, this.particles[j].color);

                    this.ctx.strokeStyle = gradient;
                    this.ctx.lineWidth = Math.max(1, (120 - distance) / 60);
                    this.ctx.shadowBlur = 5;
                    this.ctx.shadowColor = this.particles[i].color;

                    // Draw curved connection for morphing particles
                    if (this.particles[i].type === 'morphing' || this.particles[j].type === 'morphing') {
                        this.drawCurvedConnection(this.particles[i], this.particles[j]);
                    } else {
                        this.ctx.beginPath();
                        this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
                        this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
                        this.ctx.stroke();
                    }

                    this.ctx.restore();
                }
            }
        }
    }

    drawCurvedConnection(particle1, particle2) {
        const midX = (particle1.x + particle2.x) / 2;
        const midY = (particle1.y + particle2.y) / 2;
        const offset = Math.sin(Date.now() * 0.005) * 20;

        this.ctx.beginPath();
        this.ctx.moveTo(particle1.x, particle1.y);
        this.ctx.quadraticCurveTo(midX + offset, midY + offset, particle2.x, particle2.y);
        this.ctx.stroke();
    }

    drawWaveEffects() {
        const time = Date.now() * 0.001;
        const waveCount = 3;

        for (let w = 0; w < waveCount; w++) {
            this.ctx.save();
            this.ctx.globalAlpha = 0.1;
            this.ctx.strokeStyle = `hsl(${180 + w * 60}, 100%, 50%)`;
            this.ctx.lineWidth = 2;
            this.ctx.shadowBlur = 10;
            this.ctx.shadowColor = this.ctx.strokeStyle;

            this.ctx.beginPath();
            for (let x = 0; x <= this.canvas.width; x += 5) {
                const y = this.canvas.height / 2 +
                         Math.sin((x * 0.01) + (time * 2) + (w * Math.PI / 3)) * 50 +
                         Math.sin((x * 0.005) + (time * 1.5) + (w * Math.PI / 2)) * 30;

                if (x === 0) {
                    this.ctx.moveTo(x, y);
                } else {
                    this.ctx.lineTo(x, y);
                }
            }
            this.ctx.stroke();
            this.ctx.restore();
        }
    }
    
    // Enhanced scroll animations
    setupScrollAnimations() {
        let ticking = false;

        const updateScrollAnimations = () => {
            const scrolled = window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            const scrollProgress = scrolled / (documentHeight - windowHeight);

            // Enhanced parallax effect for floating elements
            const floatingElements = document.querySelectorAll('.floating-icon');
            floatingElements.forEach((element, index) => {
                const speed = 0.3 + (index * 0.15);
                const rotation = scrolled * 0.1 * (index + 1);
                const scale = 1 + Math.sin(scrolled * 0.01 + index) * 0.1;

                element.style.transform = `
                    translateY(${scrolled * speed}px)
                    rotate(${rotation}deg)
                    scale(${scale})
                `;
                element.style.opacity = 1 - (scrollProgress * 0.5);
            });

            // Animate hero avatar based on scroll
            const avatarContainer = document.querySelector('.avatar-container');
            if (avatarContainer) {
                const heroSection = document.getElementById('home');
                const heroRect = heroSection.getBoundingClientRect();
                const heroProgress = Math.max(0, Math.min(1, -heroRect.top / heroRect.height));

                avatarContainer.style.transform = `
                    translateY(${heroProgress * 50}px)
                    rotateY(${heroProgress * 15}deg)
                    scale(${1 - heroProgress * 0.1})
                `;
            }

            // Animate neural network canvas particles based on scroll
            if (this.particles) {
                this.particles.forEach((particle, index) => {
                    particle.vx += Math.sin(scrolled * 0.01 + index) * 0.001;
                    particle.vy += Math.cos(scrolled * 0.01 + index) * 0.001;
                });
            }

            // Add scroll-based color shifts
            this.updateScrollBasedColors(scrollProgress);

            ticking = false;
        };

        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestScrollUpdate);
    }

    updateScrollBasedColors(scrollProgress) {
        const root = document.documentElement;
        const hueShift = scrollProgress * 60; // Shift hue by up to 60 degrees

        // Subtle color transitions based on scroll position
        const primaryHue = 180 + hueShift; // Start from cyan
        const secondaryHue = 300 + hueShift; // Start from magenta
        const accentHue = 120 + hueShift; // Start from green

        root.style.setProperty('--primary-color', `hsl(${primaryHue}, 100%, 50%)`);
        root.style.setProperty('--secondary-color', `hsl(${secondaryHue}, 100%, 50%)`);
        root.style.setProperty('--accent-color', `hsl(${accentHue}, 100%, 50%)`);
    }
    
    // Typewriter effect
    typewriter(element, texts, speed = 100, delay = 2000) {
        let textIndex = 0;
        let charIndex = 0;
        let isDeleting = false;
        
        const type = () => {
            const currentText = texts[textIndex];
            
            if (isDeleting) {
                element.textContent = currentText.substring(0, charIndex - 1);
                charIndex--;
            } else {
                element.textContent = currentText.substring(0, charIndex + 1);
                charIndex++;
            }
            
            let typeSpeed = speed;
            
            if (isDeleting) {
                typeSpeed /= 2;
            }
            
            if (!isDeleting && charIndex === currentText.length) {
                typeSpeed = delay;
                isDeleting = true;
            } else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                textIndex = (textIndex + 1) % texts.length;
                typeSpeed = 500;
            }
            
            setTimeout(type, typeSpeed);
        };
        
        type();
    }
    
    // Smooth scroll to element
    scrollToElement(targetId, offset = 70) {
        const target = document.getElementById(targetId);
        if (!target) return;
        
        const targetPosition = target.offsetTop - offset;
        const startPosition = window.pageYOffset;
        const distance = targetPosition - startPosition;
        const duration = 800;
        let start = null;
        
        const animation = (currentTime) => {
            if (start === null) start = currentTime;
            const timeElapsed = currentTime - start;
            const run = this.easeInOutQuad(timeElapsed, startPosition, distance, duration);
            window.scrollTo(0, run);
            
            if (timeElapsed < duration) {
                requestAnimationFrame(animation);
            }
        };
        
        requestAnimationFrame(animation);
    }
    
    // Easing function
    easeInOutQuad(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    // Add interactive effects
    addInteractiveEffects() {
        // Click to create particle burst
        document.addEventListener('click', (e) => {
            if (this.canvas && e.target !== this.canvas) return;

            this.createParticleBurst(e.clientX, e.clientY);
        });

        // Double click for special effect
        document.addEventListener('dblclick', (e) => {
            this.createRippleEffect(e.clientX, e.clientY);
        });

        // Keyboard interactions
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                this.createRandomBurst();
            }
        });
    }

    createParticleBurst(x, y) {
        const burstParticles = [];
        const particleCount = 15;

        for (let i = 0; i < particleCount; i++) {
            const angle = (i / particleCount) * Math.PI * 2;
            const speed = Math.random() * 5 + 2;

            burstParticles.push({
                x: x,
                y: y,
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                size: Math.random() * 4 + 2,
                opacity: 1,
                color: this.getRandomColor(),
                life: 1,
                decay: 0.02
            });
        }

        this.animateBurstParticles(burstParticles);
    }

    animateBurstParticles(burstParticles) {
        const animate = () => {
            burstParticles.forEach((particle, index) => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.vx *= 0.98;
                particle.vy *= 0.98;
                particle.life -= particle.decay;
                particle.opacity = particle.life;

                if (particle.life <= 0) {
                    burstParticles.splice(index, 1);
                }
            });

            // Draw burst particles
            this.ctx.save();
            burstParticles.forEach(particle => {
                this.ctx.globalAlpha = particle.opacity;
                this.ctx.fillStyle = particle.color;
                this.ctx.shadowBlur = 10;
                this.ctx.shadowColor = particle.color;
                this.ctx.beginPath();
                this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                this.ctx.fill();
            });
            this.ctx.restore();

            if (burstParticles.length > 0) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    createRippleEffect(x, y) {
        const ripples = [];
        const rippleCount = 3;

        for (let i = 0; i < rippleCount; i++) {
            ripples.push({
                x: x,
                y: y,
                radius: 0,
                maxRadius: 100 + i * 50,
                opacity: 1,
                color: this.getRandomColor(),
                speed: 2 + i * 0.5
            });
        }

        this.animateRipples(ripples);
    }

    animateRipples(ripples) {
        const animate = () => {
            ripples.forEach((ripple, index) => {
                ripple.radius += ripple.speed;
                ripple.opacity = 1 - (ripple.radius / ripple.maxRadius);

                if (ripple.radius >= ripple.maxRadius) {
                    ripples.splice(index, 1);
                }
            });

            // Draw ripples
            this.ctx.save();
            ripples.forEach(ripple => {
                this.ctx.globalAlpha = ripple.opacity;
                this.ctx.strokeStyle = ripple.color;
                this.ctx.lineWidth = 2;
                this.ctx.shadowBlur = 15;
                this.ctx.shadowColor = ripple.color;
                this.ctx.beginPath();
                this.ctx.arc(ripple.x, ripple.y, ripple.radius, 0, Math.PI * 2);
                this.ctx.stroke();
            });
            this.ctx.restore();

            if (ripples.length > 0) {
                requestAnimationFrame(animate);
            }
        };

        animate();
    }

    createRandomBurst() {
        const x = Math.random() * this.canvas.width;
        const y = Math.random() * this.canvas.height;
        this.createParticleBurst(x, y);
    }

    // Enhanced project card animation
    animateProjectCard(card) {
        card.style.transform = 'translateY(50px) rotateX(10deg)';
        card.style.opacity = '0';

        setTimeout(() => {
            card.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.transform = 'translateY(0) rotateX(0deg)';
            card.style.opacity = '1';
        }, 100);

        // Add hover effect
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) rotateX(5deg) rotateY(2deg)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) rotateX(0deg) rotateY(0deg)';
        });
    }

    // Cleanup
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }

        if (this.observer) {
            this.observer.disconnect();
        }

        window.removeEventListener('resize', this.resizeCanvas);
    }
}

// Initialize animation controller
const animationController = new AnimationController();

// Utility functions for external use
window.scrollToSection = (sectionId) => {
    animationController.scrollToElement(sectionId);
};

window.animateElement = (element, animation) => {
    element.style.animation = animation;
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
