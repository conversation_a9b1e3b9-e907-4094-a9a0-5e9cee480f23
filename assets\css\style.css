/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* AI Theme Colors */
    --primary-color: #00ffff;
    --secondary-color: #ff00ff;
    --accent-color: #00ff00;
    --warning-color: #ffff00;
    --danger-color: #ff0040;
    
    /* Background Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-glass: rgba(255, 255, 255, 0.05);
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8b8b8;
    --text-muted: #666666;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-secondary: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    --gradient-bg: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    
    /* Shadows */
    --shadow-glow: 0 0 20px rgba(0, 255, 255, 0.3);
    --shadow-neon: 0 0 30px rgba(255, 0, 255, 0.4);
    --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.3);
    
    /* Fonts */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Rajdhani', sans-serif;
    
    /* Transitions */
    --transition-fast: 0.3s ease;
    --transition-smooth: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

html {
    scroll-behavior: smooth;
    overflow-x: hidden;
}

body {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(0, 255, 0, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* ===== LOADING SCREEN ===== */
#loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

#loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.ai-logo {
    margin-bottom: 2rem;
}

.neural-network {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.neural-network .node {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    box-shadow: var(--shadow-glow);
    animation: pulse 2s infinite;
}

.neural-network .node:nth-child(1) {
    top: 0;
    left: 40px;
}

.neural-network .node:nth-child(2) {
    bottom: 0;
    left: 0;
    animation-delay: 0.5s;
}

.neural-network .node:nth-child(3) {
    bottom: 0;
    right: 0;
    animation-delay: 1s;
}

.neural-network .connection {
    position: absolute;
    height: 2px;
    background: var(--gradient-secondary);
    animation: flow 3s infinite;
}

.neural-network .connection:nth-child(4) {
    top: 20px;
    left: 50px;
    width: 40px;
    transform: rotate(45deg);
}

.neural-network .connection:nth-child(5) {
    top: 20px;
    left: 10px;
    width: 40px;
    transform: rotate(-45deg);
    animation-delay: 1.5s;
}

.loading-content h2 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    margin-bottom: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.loading-bar {
    width: 300px;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.loading-progress {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    animation: loading 3s ease-in-out infinite;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 15, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
    z-index: 1000;
    transition: var(--transition-smooth);
}

.navbar.scrolled {
    background: rgba(10, 10, 15, 0.95);
    box-shadow: var(--shadow-soft);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: var(--font-primary);
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 2rem;
    animation: rotate 10s linear infinite;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition-fast);
    padding: 0.5rem 0;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.search-btn,
.help-btn,
.theme-btn,
.fullscreen-btn,
.resume-btn {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    font-family: var(--font-secondary);
    position: relative;
}

.resume-btn {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    width: auto;
    gap: 0.5rem;
}

.search-btn:hover,
.help-btn:hover,
.theme-btn:hover,
.fullscreen-btn:hover,
.resume-btn:hover {
    background: rgba(0, 255, 255, 0.1);
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

.search-btn:hover {
    background: rgba(0, 255, 0, 0.1);
    border-color: var(--accent-color);
}

.help-btn:hover {
    background: rgba(255, 255, 0, 0.1);
    border-color: var(--warning-color);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* ===== PROGRESS BAR ===== */
.progress-bar {
    position: fixed;
    top: 70px;
    left: 0;
    height: 3px;
    background: var(--gradient-primary);
    z-index: 999;
    transition: width 0.1s ease;
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: var(--bg-primary);
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-smooth);
    box-shadow: var(--shadow-glow);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-5px) scale(1.1);
    box-shadow: var(--shadow-neon);
}

/* ===== BREADCRUMB NAVIGATION ===== */
.breadcrumb {
    position: fixed;
    top: 80px;
    left: 2rem;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-20px);
    transition: all var(--transition-smooth);
}

.breadcrumb.visible {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.breadcrumb-container {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 25px;
    padding: 0.5rem 1rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--primary-color);
    font-size: 0.9rem;
    font-weight: 500;
}

.breadcrumb-item i {
    font-size: 1rem;
}

/* ===== KEYBOARD HELPER ===== */
.keyboard-helper {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--bg-secondary);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    padding: 2rem;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scale(0.8);
    transition: all var(--transition-smooth);
    box-shadow: var(--shadow-neon);
    max-width: 400px;
    width: 90%;
}

.keyboard-helper.visible {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.keyboard-helper-content h4 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
    font-family: var(--font-primary);
}

.shortcut-list {
    display: grid;
    gap: 0.8rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background: var(--bg-glass);
    border-radius: 8px;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.shortcut-item kbd {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.3rem 0.6rem;
    border-radius: 5px;
    font-family: var(--font-primary);
    font-weight: bold;
    min-width: 30px;
    text-align: center;
}

.shortcut-item span {
    color: var(--text-secondary);
}

/* ===== GLOBAL SEARCH OVERLAY ===== */
.search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-smooth);
}

.search-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.search-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding-top: 5rem;
}

.search-header h3 {
    color: var(--primary-color);
    font-family: var(--font-primary);
    font-size: 2rem;
}

.search-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.search-close:hover {
    color: var(--primary-color);
    background: var(--bg-glass);
}

.search-input-container {
    position: relative;
    margin-bottom: 2rem;
}

.global-search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    background: var(--bg-glass);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 50px;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-family: var(--font-secondary);
    outline: none;
    transition: var(--transition-fast);
}

.global-search-input:focus {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.search-input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1.2rem;
}

.search-results {
    flex: 1;
    overflow-y: auto;
}

.search-suggestions h4,
.search-results-list h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.suggestion-item,
.search-result-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.suggestion-item:hover,
.search-result-item:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateX(5px);
}

.suggestion-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
}

.result-type {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.search-result-item h4 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1rem;
}

.search-result-item p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.no-results {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#neural-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    animation: float 6s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.floating-icon:nth-child(3) {
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.floating-icon:nth-child(4) {
    top: 40%;
    right: 30%;
    animation-delay: 3s;
}

.floating-icon:nth-child(5) {
    bottom: 20%;
    right: 10%;
    animation-delay: 4s;
}

.floating-icon::before {
    content: attr(data-icon);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    z-index: 2;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 900;
    line-height: 1;
    margin-bottom: 1rem;
}

.title-line {
    display: block;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: glow 3s ease-in-out infinite alternate;
}

.title-subtitle {
    display: block;
    font-size: 0.4em;
    color: var(--text-secondary);
    letter-spacing: 0.2em;
    margin-top: 0.5rem;
}

.typewriter-container {
    height: 3rem;
    margin: 2rem 0;
    display: flex;
    align-items: center;
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--accent-color);
}

.typewriter {
    border-right: 2px solid var(--accent-color);
    padding-right: 0.5rem;
}

.cursor {
    animation: blink 1s infinite;
    color: var(--accent-color);
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin-bottom: 3rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.hero-avatar {
    z-index: 2;
}

.avatar-container {
    position: relative;
    width: 300px;
    height: 300px;
}

.avatar-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
    position: relative;
    z-index: 2;
    background: var(--bg-secondary);
}

.avatar-image:error {
    content: "👤";
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    background: var(--bg-secondary);
}

.avatar-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    opacity: 0.3;
    animation: pulse 3s ease-in-out infinite;
    z-index: 1;
}

.avatar-ring {
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    border: 2px solid var(--secondary-color);
    border-radius: 50%;
    border-top-color: transparent;
    border-right-color: transparent;
    animation: rotate 10s linear infinite;
    z-index: 3;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-arrow {
    color: var(--primary-color);
    font-size: 2rem;
    animation: bounce 2s infinite;
    cursor: pointer;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-family: var(--font-secondary);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-smooth);
    z-index: -1;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    transform: translateY(-3px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-secondary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition-smooth);
        border-top: 1px solid rgba(0, 255, 255, 0.2);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-controls {
        gap: 0.3rem;
    }

    .search-btn,
    .help-btn,
    .theme-btn,
    .fullscreen-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .resume-btn span {
        display: none;
    }

    .resume-btn {
        width: 35px;
        height: 35px;
        padding: 0.5rem;
        border-radius: 50%;
    }

    .hamburger {
        display: flex;
    }

    .breadcrumb {
        left: 1rem;
        top: 75px;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .keyboard-helper {
        width: 95%;
        padding: 1.5rem;
    }

    .search-container {
        padding: 1rem;
    }

    .search-header {
        padding-top: 3rem;
    }

    .search-header h3 {
        font-size: 1.5rem;
    }

    .global-search-input {
        font-size: 1rem;
        padding: 0.8rem 0.8rem 0.8rem 2.5rem;
    }

    .search-input-icon {
        left: 0.8rem;
        font-size: 1rem;
    }

    .suggestion-item,
    .search-result-item {
        padding: 0.8rem;
        gap: 0.8rem;
    }

    /* Skills responsive design */
    .skills-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .skills-view-toggle {
        justify-content: center;
    }

    .chart-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-controls {
        justify-content: center;
    }

    .comparison-selectors {
        flex-direction: column;
        gap: 1rem;
    }

    .skill-selector {
        min-width: auto;
        width: 100%;
    }

    .comparison-visual {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .comparison-vs {
        order: 2;
    }

    .skills-stats {
        grid-template-columns: 1fr;
    }

    .chart-legend {
        grid-template-columns: 1fr;
    }

    #skills-chart {
        width: 100%;
        height: 300px;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-logo {
        font-size: 1.2rem;
    }

    .nav-logo i {
        font-size: 1.5rem;
    }

    .nav-controls {
        gap: 0.2rem;
    }

    .search-btn,
    .help-btn,
    .theme-btn,
    .fullscreen-btn,
    .resume-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .breadcrumb {
        left: 0.5rem;
    }

    .breadcrumb-container {
        padding: 0.3rem 0.8rem;
        font-size: 0.8rem;
    }

    .keyboard-helper {
        padding: 1rem;
    }

    .shortcut-list {
        gap: 0.5rem;
    }

    .shortcut-item {
        padding: 0.3rem;
    }

    .shortcut-item kbd {
        padding: 0.2rem 0.4rem;
        font-size: 0.8rem;
        min-width: 25px;
    }
}

/* ===== ACCESSIBILITY ===== */
.keyboard-navigation button:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== ADVANCED ANIMATIONS & 3D EFFECTS ===== */

/* Enhanced hover effects for interactive elements */
.btn,
.project-card,
.skill-card,
.contact-card,
.timeline-item {
    transform-style: preserve-3d;
    transition: all var(--transition-smooth);
}

.btn:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow:
        var(--shadow-neon),
        0 15px 35px rgba(0, 0, 0, 0.3);
}

.project-card:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 255, 255, 0.2),
        0 0 30px rgba(255, 0, 255, 0.1);
}

.skill-card:hover {
    transform: translateY(-5px) scale(1.05) rotateZ(1deg);
    box-shadow: var(--shadow-glow);
}

.contact-card:hover {
    transform: translateY(-8px) rotateX(3deg);
    box-shadow: var(--shadow-neon);
}

.timeline-item:hover {
    transform: translateX(10px) rotateY(2deg);
    box-shadow: 0 10px 30px rgba(0, 255, 255, 0.2);
}

/* Morphing button effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

/* Floating animation for hero elements */
.floating-icon {
    animation: advancedFloat 8s ease-in-out infinite;
}

@keyframes advancedFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }
    25% {
        transform: translateY(-20px) rotate(5deg) scale(1.1);
    }
    50% {
        transform: translateY(-10px) rotate(-3deg) scale(0.95);
    }
    75% {
        transform: translateY(-25px) rotate(8deg) scale(1.05);
    }
}

/* Enhanced avatar animations */
.avatar-container {
    perspective: 1000px;
}

.avatar-image {
    transition: all var(--transition-smooth);
    transform-style: preserve-3d;
}

.avatar-container:hover .avatar-image {
    transform: rotateY(15deg) rotateX(5deg) scale(1.05);
}

.avatar-glow {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        var(--primary-color),
        var(--secondary-color),
        var(--accent-color),
        var(--primary-color)
    );
    animation: avatarGlow 4s linear infinite;
    z-index: -1;
    opacity: 0.7;
}

@keyframes avatarGlow {
    0% {
        transform: rotate(0deg) scale(1);
        filter: blur(20px);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
        filter: blur(25px);
    }
    100% {
        transform: rotate(360deg) scale(1);
        filter: blur(20px);
    }
}

.avatar-ring {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border: 2px solid transparent;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    animation: ringPulse 3s ease-in-out infinite;
}

@keyframes ringPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 0.5;
    }
}

/* Morphing shapes for section backgrounds */
.section {
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle,
        rgba(0, 255, 255, 0.03) 0%,
        transparent 50%
    );
    animation: morphingBackground 20s ease-in-out infinite;
    pointer-events: none;
}

@keyframes morphingBackground {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        border-radius: 50%;
    }
    25% {
        transform: rotate(90deg) scale(1.2);
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
    }
    50% {
        transform: rotate(180deg) scale(0.8);
        border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
    }
    75% {
        transform: rotate(270deg) scale(1.1);
        border-radius: 40% 60% 60% 40% / 60% 40% 60% 40%;
    }
}

/* Glitch effect for text elements */
.glitch-text {
    position: relative;
    color: var(--primary-color);
}

.glitch-text::before,
.glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.glitch-text::before {
    animation: glitch1 2s infinite;
    color: var(--secondary-color);
    z-index: -1;
}

.glitch-text::after {
    animation: glitch2 2s infinite;
    color: var(--accent-color);
    z-index: -2;
}

@keyframes glitch1 {
    0%, 100% {
        transform: translate(0);
    }
    20% {
        transform: translate(-2px, 2px);
    }
    40% {
        transform: translate(-2px, -2px);
    }
    60% {
        transform: translate(2px, 2px);
    }
    80% {
        transform: translate(2px, -2px);
    }
}

@keyframes glitch2 {
    0%, 100% {
        transform: translate(0);
    }
    20% {
        transform: translate(2px, -2px);
    }
    40% {
        transform: translate(2px, 2px);
    }
    60% {
        transform: translate(-2px, -2px);
    }
    80% {
        transform: translate(-2px, 2px);
    }
}

/* ===== ANIMATIONS ===== */
@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(10px) rotate(-5deg); }
}

@keyframes glow {
    0%, 100% { text-shadow: 0 0 20px rgba(0, 255, 255, 0.5); }
    50% { text-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(255, 0, 255, 0.3); }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

@keyframes flow {
    0% { opacity: 0; transform: scale(0); }
    50% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
}

/* ===== SECTIONS ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

section {
    padding: 5rem 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: var(--font-primary);
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.section-line {
    width: 100px;
    height: 4px;
    background: var(--gradient-secondary);
    margin: 0 auto 1rem;
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about {
    background: var(--bg-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.about-card h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.about-card p {
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-glass);
    border: 1px solid rgba(255, 0, 255, 0.2);
    border-radius: 15px;
    transition: var(--transition-smooth);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-neon);
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--secondary-color);
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.ai-visualization {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
}

.brain-container {
    position: relative;
    width: 300px;
    height: 200px;
}

.brain-hemisphere {
    position: absolute;
    width: 120px;
    height: 150px;
    border: 3px solid var(--primary-color);
    border-radius: 60px 60px 60px 60px;
    background: var(--bg-glass);
}

.brain-hemisphere.left {
    left: 0;
    border-right: none;
    border-radius: 60px 0 0 60px;
}

.brain-hemisphere.right {
    right: 0;
    border-left: none;
    border-radius: 0 60px 60px 0;
}

.neural-path {
    position: absolute;
    width: 80%;
    height: 2px;
    background: var(--gradient-secondary);
    border-radius: 1px;
    animation: neural-pulse 3s ease-in-out infinite;
}

.neural-path:nth-child(1) {
    top: 30%;
    left: 10%;
    animation-delay: 0s;
}

.neural-path:nth-child(2) {
    top: 50%;
    left: 10%;
    animation-delay: 1s;
}

.neural-path:nth-child(3) {
    top: 70%;
    left: 10%;
    animation-delay: 2s;
}

.brain-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--bg-primary);
    animation: pulse 2s ease-in-out infinite;
}

/* ===== ENHANCED SKILLS SECTION ===== */
.skills-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.skills-view-toggle {
    display: flex;
    gap: 0.5rem;
    background: var(--bg-glass);
    border-radius: 25px;
    padding: 0.3rem;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.view-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 1rem;
}

.view-btn.active,
.view-btn:hover {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    transform: scale(1.05);
}

/* Skills Chart Container */
.skills-chart-container {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.chart-header h3 {
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-type-btn {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.9rem;
}

.chart-type-btn.active,
.chart-type-btn:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    border-color: var(--primary-color);
}

.chart-wrapper {
    display: flex;
    justify-content: center;
    margin: 2rem 0;
}

#skills-chart {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    background: var(--bg-secondary);
}

.chart-legend {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.5rem;
    background: var(--bg-glass);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    flex-shrink: 0;
}

.legend-label {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

.legend-value {
    color: var(--primary-color);
    font-weight: bold;
}

/* Skills Comparison Container */
.skills-comparison-container {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.comparison-header {
    text-align: center;
    margin-bottom: 2rem;
}

.comparison-header h3 {
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin-bottom: 1.5rem;
}

.comparison-selectors {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.skill-selector {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-primary);
    padding: 0.8rem 1.2rem;
    border-radius: 15px;
    font-size: 1rem;
    min-width: 200px;
    cursor: pointer;
    transition: var(--transition-fast);
}

.skill-selector:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.vs-text {
    color: var(--accent-color);
    font-weight: bold;
    font-size: 1.2rem;
    font-family: var(--font-primary);
}

.comparison-result {
    margin-top: 2rem;
}

.comparison-placeholder {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.comparison-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.comparison-visual {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.skill-comparison-item {
    background: var(--bg-secondary);
    border: 2px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-smooth);
}

.skill-comparison-item.winner {
    border-color: var(--accent-color);
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
    transform: scale(1.05);
}

.skill-comparison-item .skill-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.skill-comparison-item h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.skill-level-bar {
    position: relative;
    background: var(--bg-primary);
    height: 20px;
    border-radius: 10px;
    margin: 1rem 0;
    overflow: hidden;
}

.skill-level-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 1s ease;
    position: relative;
}

.skill-level-text {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    color: var(--text-primary);
    font-weight: bold;
    font-size: 0.9rem;
}

.skill-category {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin: 0;
}

.comparison-vs {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.vs-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
    font-weight: bold;
    font-size: 1.2rem;
    animation: pulse 2s infinite;
}

.comparison-stats {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
}

.comparison-stats .stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.comparison-stats .stat-label {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

.comparison-stats .stat-value {
    color: var(--primary-color);
    font-weight: bold;
}

.comparison-insights {
    background: var(--bg-glass);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.comparison-insights h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-family: var(--font-primary);
}

.insights-grid {
    display: grid;
    gap: 1rem;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: var(--bg-secondary);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.insight-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
}

/* Skills Statistics */
.skills-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.stat-card {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition-smooth);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
    font-size: 1.5rem;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
    font-family: var(--font-primary);
    transition: var(--transition-fast);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.stat-detail {
    color: var(--text-muted);
    font-size: 0.8rem;
}

/* ===== SKILLS SECTION ===== */
.skills-categories {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.category-btn {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--font-secondary);
    font-weight: 500;
}

.category-btn:hover,
.category-btn.active {
    background: rgba(0, 255, 255, 0.1);
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.skill-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.skill-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.skill-card:hover::before {
    left: 100%;
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-color);
}

.skill-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.skill-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--bg-primary);
}

.skill-info h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.skill-category {
    color: var(--text-muted);
    font-size: 0.9rem;
}

.skill-progress {
    margin-bottom: 1rem;
}

.progress-bar-container {
    background: var(--bg-secondary);
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-bar-fill {
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 4px;
    transition: width 2s ease;
    position: relative;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.skill-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== ENHANCED PROJECTS SECTION ===== */
.projects-controls {
    margin-bottom: 3rem;
}

.projects-search-container {
    position: relative;
    margin-bottom: 2rem;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.projects-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-select {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.view-controls {
    display: flex;
    gap: 0.3rem;
    background: var(--bg-glass);
    border-radius: 20px;
    padding: 0.2rem;
    border: 1px solid rgba(0, 255, 255, 0.2);
}

.view-control-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 0.5rem 0.8rem;
    border-radius: 15px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 1rem;
}

.view-control-btn.active,
.view-control-btn:hover {
    background: var(--gradient-primary);
    color: var(--bg-primary);
}

/* Projects Container */
.projects-container {
    position: relative;
}

/* Grid View */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

/* List View */
.projects-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.project-list-item {
    display: flex;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    overflow: hidden;
    transition: var(--transition-smooth);
    min-height: 150px;
}

.project-list-item:hover {
    transform: translateX(10px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-color);
}

.project-list-image {
    position: relative;
    width: 200px;
    flex-shrink: 0;
}

.project-list-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
}

.project-list-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.project-list-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
}

.project-list-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1.5rem;
    gap: 0.8rem;
    border-left: 1px solid rgba(0, 255, 255, 0.1);
    min-width: 120px;
}

/* Enhanced Project Cards */
.project-card {
    position: relative;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition-smooth);
    transform-style: preserve-3d;
}

.project-card:hover {
    transform: translateY(-10px) rotateX(5deg) rotateY(2deg);
    box-shadow:
        0 20px 40px rgba(0, 255, 255, 0.2),
        0 0 30px rgba(255, 0, 255, 0.1);
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.project-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-smooth);
}

.project-card:hover .project-image {
    transform: scale(1.1);
}

.image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    gap: 0.5rem;
}

.image-placeholder i {
    font-size: 2rem;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.overlay-btn {
    background: var(--gradient-primary);
    border: none;
    color: var(--bg-primary);
    padding: 1rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-fast);
    transform: translateY(20px);
}

.project-card:hover .overlay-btn {
    transform: translateY(0);
}

.overlay-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-neon);
}

.project-content {
    padding: 1.5rem;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    gap: 1rem;
}

.project-title {
    color: var(--primary-color);
    font-family: var(--font-primary);
    font-size: 1.3rem;
    margin: 0;
    line-height: 1.2;
}

.project-category {
    background: var(--gradient-secondary);
    color: var(--bg-primary);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    white-space: nowrap;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.project-tag {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 255, 255, 0.2);
    transition: var(--transition-fast);
}

.project-tag:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
}

.project-tag.more {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    font-weight: bold;
}

.project-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.project-link {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 255, 255, 0.3);
    color: var(--text-primary);
    padding: 0.8rem;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    cursor: pointer;
}

.project-link:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

.project-list-actions .project-link {
    width: auto;
    border-radius: 20px;
    padding: 0.6rem 1rem;
    gap: 0.5rem;
    justify-content: flex-start;
}

/* Projects Statistics */
.projects-stats {
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.projects-summary {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
}

.summary-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.summary-value {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
    font-family: var(--font-primary);
}

/* ===== PROJECTS SECTION ===== */
.projects {
    background: var(--bg-secondary);
}

.projects-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    gap: 2rem;
    flex-wrap: wrap;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 25px;
    color: var(--text-primary);
    font-family: var(--font-secondary);
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.search-input::placeholder {
    color: var(--text-muted);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.filter-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: var(--bg-glass);
    border: 1px solid rgba(255, 0, 255, 0.3);
    color: var(--text-secondary);
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition-fast);
    font-family: var(--font-secondary);
}

.filter-btn:hover,
.filter-btn.active {
    background: rgba(255, 0, 255, 0.1);
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.project-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition-smooth);
    position: relative;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow);
}

.project-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-smooth);
    background: var(--bg-secondary);
}

.project-image:error {
    content: "🖼️";
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    background: var(--bg-secondary);
    color: var(--text-muted);
}

.project-card:hover .project-image {
    transform: scale(1.1);
}

.project-content {
    padding: 2rem;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.project-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.project-category {
    background: var(--gradient-secondary);
    color: var(--bg-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.project-tag {
    background: rgba(0, 255, 0, 0.1);
    color: var(--accent-color);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    border: 1px solid rgba(0, 255, 0, 0.3);
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.project-link:hover {
    color: var(--primary-color);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(10, 10, 15, 0.95);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transition: var(--transition-smooth);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .avatar-container {
        width: 200px;
        height: 200px;
        margin: 0 auto;
    }

    .hero-buttons {
        justify-content: center;
    }

    .nav-controls {
        gap: 0.5rem;
    }

    .resume-btn span {
        display: none;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .projects-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-container {
        max-width: none;
    }

    .filter-buttons {
        justify-content: center;
    }
}

/* ===== EXPERIENCE SECTION ===== */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    width: 50%;
    padding: 2rem;
}

.timeline-item:nth-child(odd) {
    left: 0;
    text-align: right;
}

.timeline-item:nth-child(even) {
    left: 50%;
    text-align: left;
}

.timeline-item::before {
    content: '';
    position: absolute;
    top: 2rem;
    width: 20px;
    height: 20px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    border: 4px solid var(--bg-primary);
    z-index: 2;
}

.timeline-item:nth-child(odd)::before {
    right: -10px;
}

.timeline-item:nth-child(even)::before {
    left: -10px;
}

.timeline-content {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    position: relative;
    transition: var(--transition-smooth);
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: 2rem;
    width: 0;
    height: 0;
    border: 15px solid transparent;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -30px;
    border-left-color: rgba(0, 255, 255, 0.2);
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -30px;
    border-right-color: rgba(0, 255, 255, 0.2);
}

.timeline-date {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
}

.timeline-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.timeline-company {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.timeline-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.timeline-skill {
    background: rgba(255, 0, 255, 0.1);
    color: var(--secondary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    border: 1px solid rgba(255, 0, 255, 0.3);
}

/* ===== CONTACT SECTION ===== */
.contact {
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-card {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.contact-card h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    transition: var(--transition-fast);
}

.contact-item:hover {
    transform: translateX(10px);
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--bg-primary);
}

.contact-item div span:first-child {
    display: block;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.contact-item div span:last-child,
.contact-item div a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
}

.contact-item div a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.social-link {
    width: 50px;
    height: 50px;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-smooth);
    z-index: -1;
}

.social-link:hover::before {
    left: 0;
}

.social-link:hover {
    color: var(--bg-primary);
    transform: translateY(-5px);
    box-shadow: var(--shadow-glow);
}

.contact-form {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 0, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: transparent;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    color: var(--text-primary);
    font-family: var(--font-secondary);
    transition: var(--transition-fast);
    resize: none;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--shadow-glow);
}

.form-group label {
    position: absolute;
    top: 1rem;
    left: 1rem;
    color: var(--text-muted);
    transition: var(--transition-fast);
    pointer-events: none;
    background: var(--bg-primary);
    padding: 0 0.5rem;
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:valid + label,
.form-group textarea:valid + label {
    top: -0.5rem;
    left: 0.5rem;
    font-size: 0.8rem;
    color: var(--primary-color);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid rgba(0, 255, 255, 0.2);
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-text {
    color: var(--text-secondary);
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

/* ===== MODAL ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.modal-header h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.modal-close:hover {
    color: var(--primary-color);
}

.modal-body {
    padding: 2rem;
}

/* ===== PROJECT MODAL STYLES ===== */
.project-modal-content {
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow-y: auto;
}

.project-modal-content-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.project-modal-image {
    position: relative;
}

.project-modal-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

.featured-badge-large {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.6rem 1.2rem;
    border-radius: 25px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.project-modal-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.project-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.project-category-badge {
    background: var(--gradient-secondary);
    color: var(--bg-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.status-featured {
    background: var(--gradient-primary);
    color: var(--bg-primary);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.project-description-full h4,
.project-technologies h4,
.project-features h4,
.project-links-expanded h4 {
    color: var(--primary-color);
    font-family: var(--font-primary);
    margin-bottom: 0.8rem;
    font-size: 1.1rem;
}

.project-description-full p {
    color: var(--text-secondary);
    line-height: 1.7;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: var(--bg-secondary);
    color: var(--text-primary);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    border: 1px solid rgba(0, 255, 255, 0.2);
    transition: var(--transition-fast);
}

.tech-tag:hover {
    background: var(--primary-color);
    color: var(--bg-primary);
    transform: scale(1.05);
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    color: var(--text-secondary);
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
    position: relative;
    padding-left: 1.5rem;
}

.features-list li:before {
    content: '▶';
    color: var(--primary-color);
    position: absolute;
    left: 0;
    top: 0.5rem;
}

.features-list li:last-child {
    border-bottom: none;
}

.links-grid {
    display: grid;
    gap: 1rem;
}

.project-link-expanded {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: var(--bg-glass);
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 15px;
    padding: 1rem;
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition-smooth);
}

.project-link-expanded:hover {
    background: rgba(0, 255, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateX(5px);
}

.project-link-expanded i {
    font-size: 1.5rem;
    color: var(--primary-color);
    width: 30px;
    text-align: center;
}

.link-info {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.link-title {
    font-weight: bold;
    color: var(--text-primary);
}

.link-desc {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.project-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-glass);
    border-radius: 15px;
    border: 1px solid rgba(0, 255, 255, 0.1);
}

.project-stats .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.project-stats .stat-item i {
    color: var(--primary-color);
}

.resume-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.resume-viewer {
    width: 100%;
    height: 600px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    border-radius: 12px;
    background: var(--bg-primary);
}

/* ===== ADDITIONAL ANIMATIONS ===== */
@keyframes neural-pulse {
    0%, 100% { opacity: 0.3; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.2); }
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 100px;
    right: 2rem;
    z-index: 10001;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.notification-content {
    background: var(--bg-secondary);
    border: 1px solid rgba(0, 255, 255, 0.3);
    border-radius: 12px;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-soft);
    backdrop-filter: blur(20px);
}

.notification-success .notification-content {
    border-color: rgba(0, 255, 0, 0.5);
}

.notification-error .notification-content {
    border-color: rgba(255, 0, 0, 0.5);
}

.notification-content i {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.notification-success .notification-content i {
    color: var(--accent-color);
}

.notification-error .notification-content i {
    color: var(--danger-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition-fast);
    margin-left: auto;
}

.notification-close:hover {
    color: var(--text-primary);
}

/* ===== UTILITY CLASSES ===== */
.animate-in {
    animation: fadeInUp 0.6s ease forwards;
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
}

/* ===== ADDITIONAL ANIMATIONS ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ===== RESPONSIVE DESIGN ADDITIONS ===== */
@media (max-width: 768px) {
    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        width: 100%;
        left: 0 !important;
        text-align: left;
        padding-left: 60px;
    }

    .timeline-item::before {
        left: 10px !important;
    }

    .timeline-content::before {
        left: -30px !important;
        border-right-color: rgba(0, 255, 255, 0.2) !important;
        border-left-color: transparent !important;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .social-links {
        justify-content: flex-start;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }

    .resume-actions {
        flex-direction: column;
    }

    .notification {
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}
