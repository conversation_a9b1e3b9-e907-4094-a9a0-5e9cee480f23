# Project Images Setup Guide

## 📁 Folder Structure
```
assets/
└── images/
    └── projects/
        ├── skin-disease-prediction.jpg
        ├── crop-detection.jpg
        ├── gesture-media-player.jpg
        ├── price-monitor.jpg
        ├── portfolio-website.jpg
        └── chat-application.jpg
```

## 🖼️ Image Requirements

### **Recommended Image Specifications:**
- **Format**: JPG or PNG
- **Dimensions**: 600x300 pixels (2:1 aspect ratio)
- **File Size**: Under 500KB for optimal loading
- **Quality**: High resolution, professional appearance

## 🎯 Project-Specific Image Recommendations

### 1. **skin-disease-prediction.jpg**
- **Theme**: Medical/Healthcare/Dermatology
- **Suggestions**: 
  - Medical examination of skin
  - Dermatology analysis
  - Healthcare technology
  - Medical AI/ML visualization

### 2. **crop-detection.jpg**
- **Theme**: Agriculture/Farming
- **Suggestions**:
  - Green agricultural fields
  - Crop analysis
  - Farm technology
  - Agricultural AI/ML

### 3. **gesture-media-player.jpg**
- **Theme**: Hand Gestures/Computer Vision
- **Suggestions**:
  - Hands making gestures
  - Computer vision tracking
  - Hand tracking visualization
  - AI gesture recognition

### 4. **price-monitor.jpg**
- **Theme**: E-commerce/Shopping
- **Suggestions**:
  - Shopping cart
  - Price tags
  - E-commerce dashboard
  - Online shopping interface

### 5. **portfolio-website.jpg**
- **Theme**: Web Development
- **Suggestions**:
  - Code editor screen
  - Web development workspace
  - Responsive website design
  - Programming setup

### 6. **chat-application.jpg**
- **Theme**: Communication/Messaging
- **Suggestions**:
  - Chat interface
  - Message bubbles
  - Real-time communication
  - Social media interface

## 🔄 How to Replace Images

1. **Find appropriate images** from:
   - Unsplash (free high-quality images)
   - Your own project screenshots
   - Stock photo websites
   - AI-generated images

2. **Download and rename** the images to match the exact filenames above

3. **Replace the placeholder files** in this folder

4. **Test your website** to ensure images load correctly

## ✅ Current Status
- ✅ Folder structure created
- ✅ Local image paths configured in data.js
- ⏳ **Next Step**: Replace placeholder files with actual images

## 🚀 Benefits of Local Images
- ✅ **Faster loading** - No external dependencies
- ✅ **Reliable** - Always available offline
- ✅ **Customizable** - Full control over image quality
- ✅ **Professional** - Consistent branding and style
