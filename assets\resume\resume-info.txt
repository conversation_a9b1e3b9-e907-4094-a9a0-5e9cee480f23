RESUME PDF INTEGRATION INSTRUCTIONS
===================================

To add your actual resume PDF to the website:

1. Replace this file with your actual resume PDF
2. Name your PDF file: "Sanjai_S_Resume.pdf"
3. Place it in this directory: assets/resume/

The website will automatically:
- Allow visitors to download your resume
- Display it in an online viewer
- Show a professional modal with viewing options

Current Configuration:
- PDF Path: assets/resume/Sanjai_S_Resume.pdf
- Download Filename: Sanjai_S_AI_Developer_Resume.pdf
- Online Viewer: Google Docs Viewer

To customize the resume configuration:
- Edit the resumeConfig object in assets/js/data.js
- Update the pdfUrl, downloadFilename, and viewerUrl as needed

Features:
✓ Download functionality
✓ Online PDF viewer
✓ Responsive modal design
✓ Professional presentation
✓ Mobile-friendly interface

Note: For the online viewer to work properly, your website needs to be hosted on a web server (not just opened as a local file).
